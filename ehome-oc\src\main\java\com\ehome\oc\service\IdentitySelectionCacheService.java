package com.ehome.oc.service;

import com.ehome.oc.domain.dto.IdentitySelectionData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 身份选择缓存服务
 * 
 * 管理身份选择过程中的临时数据缓存，提供缓存的存储、获取、清理功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class IdentitySelectionCacheService {
    
    private static final Logger logger = LoggerFactory.getLogger(IdentitySelectionCacheService.class);
    
    /** 缓存过期时间：5分钟 */
    private static final long CACHE_TIMEOUT_MILLIS = 5 * 60 * 1000;
    
    /** 缓存清理间隔：1分钟 */
    private static final long CLEANUP_INTERVAL_MINUTES = 1;
    
    /** 身份选择缓存存储 */
    private final Map<String, IdentitySelectionData> cache = new ConcurrentHashMap<>();
    
    /** 缓存清理定时器 */
    private ScheduledExecutorService cacheCleanupExecutor;
    
    /**
     * 初始化缓存清理定时器
     */
    @PostConstruct
    public void init() {
        cacheCleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "identity-selection-cache-cleanup");
            thread.setDaemon(true);
            return thread;
        });
        
        // 每分钟清理一次过期缓存
        cacheCleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredCache, 
            CLEANUP_INTERVAL_MINUTES, CLEANUP_INTERVAL_MINUTES, TimeUnit.MINUTES);
        
        logger.info("身份选择缓存服务已启动，缓存过期时间: {}分钟", CACHE_TIMEOUT_MILLIS / 60000);
    }
    
    /**
     * 销毁缓存清理定时器
     */
    @PreDestroy
    public void destroy() {
        if (cacheCleanupExecutor != null && !cacheCleanupExecutor.isShutdown()) {
            cacheCleanupExecutor.shutdown();
            try {
                if (!cacheCleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cacheCleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cacheCleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        cache.clear();
        logger.info("身份选择缓存服务已关闭");
    }
    
    /**
     * 存储身份选择数据
     * 
     * @param selectionData 身份选择数据
     * @return 生成的选择ID
     */
    public String storeSelectionData(IdentitySelectionData selectionData) {
        String selectionId = UUID.randomUUID().toString();
        cache.put(selectionId, selectionData);
        
        logger.info("存储身份选择数据: selectionId={}, data={}", selectionId, selectionData);
        return selectionId;
    }
    
    /**
     * 获取并移除身份选择数据
     * 
     * @param selectionId 选择ID
     * @return 身份选择数据，如果不存在或已过期则返回null
     */
    public IdentitySelectionData getAndRemoveSelectionData(String selectionId) {
        if (selectionId == null || selectionId.trim().isEmpty()) {
            logger.warn("获取身份选择数据失败: selectionId为空");
            return null;
        }
        
        IdentitySelectionData data = cache.remove(selectionId);
        if (data == null) {
            logger.warn("获取身份选择数据失败: selectionId={} 不存在或已过期", selectionId);
            return null;
        }
        
        // 检查是否过期
        if (data.isExpired(CACHE_TIMEOUT_MILLIS)) {
            logger.warn("获取身份选择数据失败: selectionId={} 已过期", selectionId);
            return null;
        }
        
        logger.info("获取身份选择数据成功: selectionId={}", selectionId);
        return data;
    }
    
    /**
     * 获取身份选择数据（不移除）
     * 
     * @param selectionId 选择ID
     * @return 身份选择数据，如果不存在或已过期则返回null
     */
    public IdentitySelectionData getSelectionData(String selectionId) {
        if (selectionId == null || selectionId.trim().isEmpty()) {
            return null;
        }
        
        IdentitySelectionData data = cache.get(selectionId);
        if (data == null || data.isExpired(CACHE_TIMEOUT_MILLIS)) {
            return null;
        }
        
        return data;
    }
    
    /**
     * 移除身份选择数据
     * 
     * @param selectionId 选择ID
     * @return 是否移除成功
     */
    public boolean removeSelectionData(String selectionId) {
        if (selectionId == null || selectionId.trim().isEmpty()) {
            return false;
        }
        
        IdentitySelectionData removed = cache.remove(selectionId);
        if (removed != null) {
            logger.info("移除身份选择数据: selectionId={}", selectionId);
            return true;
        }
        return false;
    }
    
    /**
     * 清理过期缓存
     */
    private void cleanupExpiredCache() {
        try {
            int beforeSize = cache.size();
            cache.entrySet().removeIf(entry -> 
                entry.getValue().isExpired(CACHE_TIMEOUT_MILLIS)
            );
            int afterSize = cache.size();
            
            if (beforeSize > afterSize) {
                logger.info("清理过期缓存完成: 清理前={}, 清理后={}, 清理数量={}", 
                    beforeSize, afterSize, beforeSize - afterSize);
            }
        } catch (Exception e) {
            logger.error("清理过期缓存失败", e);
        }
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存大小
     */
    public int getCacheSize() {
        return cache.size();
    }
    
    /**
     * 清空所有缓存
     */
    public void clearAll() {
        int size = cache.size();
        cache.clear();
        logger.info("清空所有身份选择缓存: 清理数量={}", size);
    }
}
