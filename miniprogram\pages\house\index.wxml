<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="container">
  <!-- 房屋列表 -->
  <view class="house-list">
    <!-- 按小区分组显示 -->
    <view class="community-group" wx:for="{{groupedHouseList}}" wx:key="communityId" wx:for-item="community">
      <!-- 小区标题 -->
      <view class="community-header">
        <van-icon name="location-o" size="28rpx" color="#1890ff" />
        <text class="community-title">{{community.displayTitle}}</text>
      </view>

      <!-- 该小区的房屋列表 -->
      <view class="house-card" wx:for="{{community.houses}}" wx:key="id" wx:for-item="house">
        <!-- 标题区域 -->
        <view class="house-header" bindtap="editHouse" data-id="{{house.id}}">
          <view class="house-title">
            <van-icon name="home-o" size="32rpx" color="#1890ff" />
            <text class="title-text">{{house.building}}/{{house.unit}}/{{house.room}}</text>
          </view>
          <van-icon name="arrow" size="24rpx" color="#ccc" />
        </view>

        <!-- 内容区域 -->
        <view class="house-content">
          <view class="content-row">
            <text class="label">住户信息：</text>
            <text class="value">{{house.ownerStr || '暂无住户'}}</text>
          </view>
          <view class="content-row">
            <text class="label">住户类型：</text>
            <text class="value">{{house.userTypeText}}</text>
          </view>
        </view>

        <!-- 底部状态区域 -->
        <view class="house-footer">
          <view
            class="default-status"
            bindtap="setDefault"
            data-id="{{house.id}}"
            data-owner-id="{{house.ownerInfo.ownerId}}"
            wx:if="{{house.checkStatus === 1}}"
          >
            <van-icon
              name="{{house.isDefaultHouse ? 'success' : 'circle'}}"
              size="32rpx"
              color="{{house.isDefaultHouse ? '#52c41a' : '#d9d9d9'}}"
            />
            <text class="status-text {{house.isDefaultHouse ? 'active' : ''}}">默认房屋</text>
          </view>
          <view
            class="default-status disabled"
            wx:else
          >
            <van-icon
              name="circle"
              size="32rpx"
              color="#d9d9d9"
            />
            <text class="status-text">默认房屋</text>
          </view>
          <view class="auth-status">
            <van-tag
              type="{{house.checkStatus === 0 ? 'warning' : (house.checkStatus === 1 ? 'success' : 'danger')}}"
              size="medium"
            >
              {{house.statusText}}
            </van-tag>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <van-button
      type="primary"
      icon="plus"
      icon-size="16px"
      custom-class="bottom-btn invite-btn"
      bindtap="addVisit"
    >
      邀请住户
    </van-button>
    <van-button
      type="info"
      icon="manager-o"
      icon-size="16px"
      custom-class="bottom-btn manage-btn"
      bindtap="manageInvites"
    >
      管理邀请
    </van-button>
    <van-button
      type="danger"
      icon="delete-o"
      icon-size="16px"
      custom-class="bottom-btn unbind-btn"
      bindtap="unbindHouse"
      wx:if="{{houseList.length > 0}}"
    >
      解绑房屋
    </van-button>

  </view>

</view>