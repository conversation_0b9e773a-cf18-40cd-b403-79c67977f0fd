package com.ehome.oc.domain;

import com.ehome.common.core.domain.model.LoginUser;

public class UserStatusData {

    private OwnerInfo ownerInfo;

    private CommunityInfo communityInfo;

    private HouseInfo houseInfo;

    private LoginUser loginUser;

    private boolean isHouseAuth;

    public OwnerInfo getOwnerInfo() {
        return ownerInfo;
    }

    public void setOwnerInfo(OwnerInfo ownerInfo) {
        this.ownerInfo = ownerInfo;
    }

    public CommunityInfo getCommunityInfo() {
        return communityInfo;
    }

    public void setCommunityInfo(CommunityInfo communityInfo) {
        this.communityInfo = communityInfo;
    }

    public HouseInfo getHouseInfo() {
        return houseInfo;
    }

    public void setHouseInfo(HouseInfo houseInfo) {
        this.houseInfo = houseInfo;
    }

    public boolean isHouseAuth() {
        return isHouseAuth;
    }
    public void setHouseAuth(boolean houseAuth) {
        isHouseAuth = houseAuth;
    }

    public LoginUser getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(LoginUser loginUser) {
        this.loginUser = loginUser;
    }
}
