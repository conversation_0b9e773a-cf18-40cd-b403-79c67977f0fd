package com.ehome.oc.domain.dto;

import java.util.Map;

/**
 * 身份选择数据缓存类
 * 
 * 用于在身份选择过程中临时存储用户登录信息，避免前端传递敏感参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class IdentitySelectionData {
    
    /** 微信用户唯一标识 */
    private final String openid;
    
    /** 微信开放平台统一标识 */
    private final String unionid;
    
    /** 微信会话密钥 */
    private final String sessionKey;
    
    /** 用户手机号 */
    private final String phoneNumber;
    
    /** 加密的用户信息 */
    private final String encryptedData;
    
    /** 解密初始向量 */
    private final String iv;
    
    /** 身份识别结果 */
    private final Map<String, Object> identityResult;
    
    /** 创建时间戳 */
    private final long createTime;

    /**
     * 构造函数
     * 
     * @param openid 微信用户唯一标识
     * @param unionid 微信开放平台统一标识
     * @param sessionKey 微信会话密钥
     * @param phoneNumber 用户手机号
     * @param encryptedData 加密的用户信息
     * @param iv 解密初始向量
     * @param identityResult 身份识别结果
     */
    public IdentitySelectionData(String openid, String unionid, String sessionKey, 
                               String phoneNumber, String encryptedData, String iv,
                               Map<String, Object> identityResult) {
        this.openid = openid;
        this.unionid = unionid;
        this.sessionKey = sessionKey;
        this.phoneNumber = phoneNumber;
        this.encryptedData = encryptedData;
        this.iv = iv;
        this.identityResult = identityResult;
        this.createTime = System.currentTimeMillis();
    }

    // Getters
    
    /**
     * 获取微信用户唯一标识
     * @return openid
     */
    public String getOpenid() { 
        return openid; 
    }
    
    /**
     * 获取微信开放平台统一标识
     * @return unionid
     */
    public String getUnionid() { 
        return unionid; 
    }
    
    /**
     * 获取微信会话密钥
     * @return sessionKey
     */
    public String getSessionKey() { 
        return sessionKey; 
    }
    
    /**
     * 获取用户手机号
     * @return phoneNumber
     */
    public String getPhoneNumber() { 
        return phoneNumber; 
    }
    
    /**
     * 获取加密的用户信息
     * @return encryptedData
     */
    public String getEncryptedData() { 
        return encryptedData; 
    }
    
    /**
     * 获取解密初始向量
     * @return iv
     */
    public String getIv() { 
        return iv; 
    }
    
    /**
     * 获取身份识别结果
     * @return identityResult
     */
    public Map<String, Object> getIdentityResult() { 
        return identityResult; 
    }
    
    /**
     * 获取创建时间戳
     * @return createTime
     */
    public long getCreateTime() { 
        return createTime; 
    }
    
    /**
     * 检查数据是否已过期
     * @param timeoutMillis 超时时间（毫秒）
     * @return 是否已过期
     */
    public boolean isExpired(long timeoutMillis) {
        return System.currentTimeMillis() - createTime > timeoutMillis;
    }
    
    @Override
    public String toString() {
        return "IdentitySelectionData{" +
                "openid='" + (openid != null ? openid.substring(0, Math.min(8, openid.length())) + "..." : "null") + '\'' +
                ", phoneNumber='" + (phoneNumber != null ? phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : "null") + '\'' +
                ", createTime=" + createTime +
                ", hasIdentityResult=" + (identityResult != null) +
                '}';
    }
}
