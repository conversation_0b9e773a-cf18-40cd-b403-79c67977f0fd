package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.poi.ExcelUtil;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.jfinal.model.OcOwnerModel;
import com.ehome.oc.domain.OwnerImport;
import com.ehome.oc.service.IOwnerImportService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 业主信息管理 控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/oc/owner")
public class OcOwnerController extends OcOwnerBaseController {
    
    private static final String PREFIX = "oc/user";

    @Autowired
    private IOwnerImportService ownerImportService;

    /**
     * 跳转到业主管理页面
     */
    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list-owner";
    }

    /**
     * 待审核住户
     * @return
     */
    @GetMapping("/check")
    public String check() {
        return PREFIX + "/check-owner";
    }

    /**
     * 业委会成员管理
     * @return
     */
    @GetMapping("/committee")
    public String committee() {
        return PREFIX + "/committee-owner";
    }

    /**
     * 跳转到新增业主页面
     */
    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add-owner";
    }

    @GetMapping("/importPage")
    public String importPage() {
        return PREFIX + "/import-page";
    }

    /**
     * 跳转到编辑业主页面
     */
    @GetMapping("/edit/{ownerId}")
    public String edit(@PathVariable("ownerId") String ownerId, ModelMap mmap) {
        OcOwnerModel owner = OcOwnerModel.dao.findById(ownerId);
        mmap.put("owner", owner.toMap());
        return PREFIX + "/edit-owner";
    }

    /**
     * 跳转到业主详情页面
     */
    @GetMapping("/detail/{ownerId}")
    public String detail(@PathVariable("ownerId") String ownerId, ModelMap mmap) {
        // 获取业主信息
        OcOwnerModel owner = OcOwnerModel.dao.findById(ownerId);
        mmap.put("owner", owner.toMap());

        // 获取业主关联的房屋列表
        List<Record> houses = Db.find(
            "SELECT r.*, h.building_name, h.unit_name, h.room, h.total_area, h.house_status " +
            "FROM eh_house_owner_rel r " +
            "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC",
            ownerId
        );
        mmap.put("houses", houses);

        // 获取业主关联的车辆列表
        List<Record> vehicles = Db.find(
            "SELECT r.*, v.plate_no, v.vehicle_brand, v.vehicle_model, v.check_status " +
            "FROM eh_vehicle_owner_rel r " +
            "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC",
            ownerId
        );
        mmap.put("vehicles", vehicles);

        return PREFIX + "/detail-owner";
    }



    /**
     * 查询业主列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);

        // 根据是否有楼栋单元过滤来决定SELECT字段
        String buildingId = params.getString("buildingId");
        String unitId = params.getString("unitId");
        String selectFields;

        if (StringUtils.isNotEmpty(buildingId) || StringUtils.isNotEmpty(unitId)) {
            // 关联查询时需要指定表别名
            selectFields = "select o.*";
        } else {
            // 普通查询
            selectFields = "select *";
        }

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            selectFields,
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/checkList")
    @ResponseBody
    public TableDataInfo checkList() {
        JSONObject params = getParams();
        EasySQL sql = buildCheckListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select r.rel_id,r.check_status,r.house_id, r.owner_id, r.rel_type, r.create_time as apply_time, r.remark as apply_remark, r.approve_info, r.file_id, " +
                "o.owner_name, o.mobile, o.gender, o.id_card, " +
                "h.combina_name, h.room, h.building_name, h.unit_name, h.use_area",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 业委会成员列表查询
     */
    @PostMapping("/committeeList")
    @ResponseBody
    public TableDataInfo committeeList() {
        JSONObject params = getParams();
        EasySQL sql = buildCommitteeListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select *",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 新增业委会成员
     */
    @PostMapping("/addCommitteeMember")
    @ResponseBody
    @Log(title = "新增业委会成员", businessType = BusinessType.UPDATE)
    public AjaxResult addCommitteeMember() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        try {
            // 检查业主是否存在
            Record owner = Db.findFirst("SELECT * FROM eh_owner WHERE owner_id = ? AND community_id = ?",
                ownerId, getSysUser().getCommunityId());

            if (owner == null) {
                return AjaxResult.error("业主不存在或不属于当前小区");
            }

            // 检查是否已经是业委会成员
            if ("2".equals(owner.getStr("role"))) {
                return AjaxResult.error("该业主已经是业委会成员");
            }

            // 更新业主角色为业委会成员
            int updateCount = Db.update("UPDATE eh_owner SET role = 2, update_time = ?, updater = ? WHERE owner_id = ?",
                DateUtils.getTime(), getSysUser().getUserName(), ownerId);

            if (updateCount > 0) {
                return AjaxResult.success("添加业委会成员成功");
            } else {
                return AjaxResult.error("添加业委会成员失败");
            }

        } catch (Exception e) {
            logger.error("添加业委会成员失败", e);
            return AjaxResult.error("添加业委会成员失败：" + e.getMessage());
        }
    }

    /**
     * 移除业委会成员
     */
    @PostMapping("/removeCommitteeMember")
    @ResponseBody
    @Log(title = "移除业委会成员", businessType = BusinessType.UPDATE)
    public AjaxResult removeCommitteeMember() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        try {
            // 检查业主是否存在且是业委会成员
            Record owner = Db.findFirst("SELECT * FROM eh_owner WHERE owner_id = ? AND community_id = ? AND role = 2",
                ownerId, getSysUser().getCommunityId());

            if (owner == null) {
                return AjaxResult.error("业委会成员不存在或不属于当前小区");
            }

            // 更新业主角色为普通业主
            int updateCount = Db.update("UPDATE eh_owner SET role = 1, update_time = ?, updater = ? WHERE owner_id = ?",
                DateUtils.getTime(), getSysUser().getUserName(), ownerId);

            if (updateCount > 0) {
                return AjaxResult.success("移除业委会成员成功");
            } else {
                return AjaxResult.error("移除业委会成员失败");
            }

        } catch (Exception e) {
            logger.error("移除业委会成员失败", e);
            return AjaxResult.error("移除业委会成员失败：" + e.getMessage());
        }
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String ownerId = params.getString("owner_id");
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        Record owner = Db.findFirst("select * from eh_owner where owner_id = ?", ownerId);
        return AjaxResult.success(null, owner.toMap());
    }

    /**
     * 新增业主信息
     */
    @PostMapping("/add")
    @ResponseBody
    @Log(title = "新增业主信息", businessType = BusinessType.INSERT)
    public AjaxResult addData() {
        JSONObject params = getParams();
        OcOwnerModel model = new OcOwnerModel();
        model.setColumns(params);

        String mobile = model.getStr("mobile");
        if (StringUtils.isNotEmpty(mobile)) {
            int count = Db.queryInt("select count(1) from eh_owner where mobile = ? and community_id = ?", mobile, getSysUser().getCommunityId());
            if (count > 0) {
                return AjaxResult.error("当前小区手机号已存在");
            }
        }
        setCreateAndUpdateInfo(model);
        model.save();
        return AjaxResult.success();
    }

    /**
     * 修改业主信息
     */
    @PostMapping("/edit")
    @ResponseBody
    @Log(title = "修改业主信息", businessType = BusinessType.UPDATE)
    public AjaxResult edit() {
        JSONObject params = getParams();
        OcOwnerModel model = new OcOwnerModel();
        model.setColumns(params);
        setUpdateInfo(model);
        boolean result = model.update();
        houseInfoService.updateOwnerHouseInfo(model.getStr("owner_id"));
        return toAjax(result);
    }

    /**
     * 删除业主信息
     */
    @PostMapping("/remove")
    @ResponseBody
    @Log(title = "删除业主信息", businessType = BusinessType.DELETE)
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数ids不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            if(OcOwnerModel.dao.deleteById(id)){
                Db.update("delete from eh_house_owner_rel where owner_id = ?", id);
            }
        }
        return success();
    }

    /**
     * 审批处理（统一方法）
     */
    @PostMapping("/approve")
    @ResponseBody
    @Log(title = "审批处理", businessType = BusinessType.UPDATE)
    public AjaxResult approve() {
        JSONObject params = getParams();
        String relIds = params.getString("relIds");
        String approveType = params.getString("approveType"); // "approve" 或 "reject"
        String rejectReason = params.getString("rejectReason");

        if (StringUtils.isEmpty(relIds)) {
            return AjaxResult.error("请选择要审批的记录");
        }

        if ("reject".equals(approveType) && StringUtils.isEmpty(rejectReason)) {
            return AjaxResult.error("审批不通过时必须填写原因");
        }

        String[] relIdArr = relIds.split(",");
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String approver = getSysUser().getLoginName();
        boolean isApprove = "approve".equals(approveType);
        int checkStatus = isApprove ? 1 : 2;

        try {
            // 构建审批信息JSON
            JSONObject approveInfo = new JSONObject();
            approveInfo.put("approve_time", now);
            approveInfo.put("approve_by", approver);
            approveInfo.put("approve_type", relIdArr.length > 1 ? "batch" : "single");
            approveInfo.put("status", isApprove ? "approved" : "rejected");
            if (!isApprove) {
                approveInfo.put("reject_reason", rejectReason);
            }

            for (String relId : relIdArr) {
                // 更新审核状态和审批信息
                Db.update("UPDATE eh_house_owner_rel SET check_status = ?, update_time = ?, update_by = ?, approve_info = ? WHERE rel_id = ?",
                    checkStatus, now, approver, approveInfo.toJSONString(), relId);

                // 如果是审批通过，需要更新相关统计
                if (isApprove) {
                    Record rel = Db.findFirst("SELECT house_id, owner_id FROM eh_house_owner_rel WHERE rel_id = ?", relId);
                    if (rel != null) {
                        houseInfoService.updateHouseOwnerInfo(rel.getStr("house_id"));
                        houseInfoService.updateOwnerHouseInfo(rel.getStr("owner_id"));
                    }
                }
            }

            String message = isApprove ? "审批通过成功" : "审批不通过成功";
            return AjaxResult.success(message);
        } catch (Exception e) {
            logger.error("审批处理失败", e);
            return AjaxResult.error("审批处理失败：" + e.getMessage());
        }
    }
    




    /**
     * 构建列表查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();

        // 检查是否需要按楼栋单元过滤
        String buildingId = params.getString("buildingId");
        String unitId = params.getString("unitId");

        if (StringUtils.isNotEmpty(buildingId) || StringUtils.isNotEmpty(unitId)) {
            // 需要关联查询房屋信息来过滤业主
            sql.append("from eh_owner o");
            sql.append("inner join eh_house_owner_rel r on o.owner_id = r.owner_id");
            sql.append("inner join eh_house_info h on r.house_id = h.house_id");
            sql.append("where r.check_status = 1");

            sql.append(getSysUser().getCommunityId(),"and o.community_id = ?");

            // 楼栋过滤
            sql.append(buildingId, "and h.building_id = ?");
            // 单元过滤
            sql.append(unitId, "and h.unit_id = ?");
        } else {
            // 普通查询，不需要关联
            sql.append("from eh_owner o where 1=1");
            sql.append(getSysUser().getCommunityId(),"and o.community_id = ?");
        }

        // 业主姓名 - 模糊查询
        sql.appendLike(params.getString("owner_name"), "and o.owner_name like ?");
        // 手机号码 - 精确匹配
        sql.appendLike(params.getString("mobile"), "and o.mobile like ?");
        // 身份证号码 - 精确匹配
        sql.append(params.getString("id_card"), "and o.id_card = ?");
        // 性别 - 精确匹配
        sql.append(params.getString("gender"), "and o.gender = ?");
        // 入住状态过滤
        sql.append(params.getString("is_live"), "and o.is_live = ?");

        // 创建时间范围查询
        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and o.create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and o.create_time <= ?");

        // 如果是关联查询，需要去重并按业主分组
        if (StringUtils.isNotEmpty(buildingId) || StringUtils.isNotEmpty(unitId)) {
            sql.append("group by o.owner_id");
        }

        // 默认按创建时间倒序
        sql.append("order by o.create_time desc");
        return sql;
    }

    /**
     * 构建业委会成员列表查询SQL
     */
    private EasySQL buildCommitteeListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_owner where 1=1");
        sql.append(getSysUser().getCommunityId(), "and community_id = ?");
        sql.append("and role = 2"); // 业委会成员

        // 搜索条件
        sql.appendLike(params.getString("owner_name"), "and owner_name like ?");
        sql.appendLike(params.getString("mobile"), "and mobile like ?");
        sql.appendLike(params.getString("house_info"), "and house_info like ?");

        // 默认按创建时间倒序
        sql.append("order by create_time desc");
        return sql;
    }

    /**
     * 构建待审核列表查询SQL
     */
    private EasySQL buildCheckListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_house_owner_rel r");
        sql.append("left join eh_owner o on r.owner_id = o.owner_id");
        sql.append("left join eh_house_info h on r.house_id = h.house_id");
        sql.append("where r.apply_flag = 1");
        sql.append(getSysUser().getCommunityId(), "and r.community_id = ?");

        sql.append(params.getString("checkStatus"),"and r.check_status = ?");

        // 业主姓名 - 模糊查询
        sql.appendLike(params.getString("owner_name"), "and o.owner_name like ?");
        // 手机号码 - 模糊查询
        sql.appendLike(params.getString("mobile"), "and o.mobile like ?");
        // 房屋信息 - 模糊查询
        String houseInfo = params.getString("house_info");
        if (StringUtils.isNotEmpty(houseInfo)) {
            sql.append("and (h.combina_name like '%" + houseInfo + "%' or h.room like '%" + houseInfo + "%')");
        }
        // 关系类型 - 精确匹配
        sql.append(params.getString("rel_type"), "and r.rel_type = ?");

        // 申请时间范围查询
        String beginTime = params.getString("beginTime");
        if (StringUtils.isNotEmpty(beginTime)) {
            sql.append(beginTime+" 00:00:00", "and r.create_time >= ?");
        }
        String endTime = params.getString("endTime");
        if (StringUtils.isNotEmpty(endTime)) {
            sql.append(endTime+" 23:59:59", "and r.create_time <= ?");
        }
        // 默认按申请时间倒序
        sql.append("order by r.create_time desc");
        return sql;
    }

    /**
     * 设置创建和更新信息
     */
    private void setCreateAndUpdateInfo(OcOwnerModel model) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        model.set("create_time", now);
        model.set("update_time", now);
        model.set("creator", loginName);
        model.set("updater", loginName);
        model.set("pms_id", getSysUser().getPmsId());
        model.set("community_id", getSysUser().getCommunityId());

    }







    /**
     * 设置更新信息
     */
    private void setUpdateInfo(OcOwnerModel model) {
        model.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        model.set("updater", getSysUser().getLoginName());
    }

    /**
     * 导入业主数据
     */
    @Log(title = "业主管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<OwnerImport> util = new ExcelUtil<OwnerImport>(OwnerImport.class);
        List<OwnerImport> ownerList = util.importExcel(file.getInputStream());

        // 设置小区ID和物业ID
        String communityId = getSysUser().getCommunityId();
        String pmsId = getSysUser().getPmsId();
        for (OwnerImport owner : ownerList) {
            owner.setCommunityId(communityId);
            owner.setPmsId(pmsId);
        }

        String message = ownerImportService.importOwner(ownerList, updateSupport, getLoginName(), communityId, pmsId);
        return AjaxResult.success(message);
    }

    /**
     * 下载业主导入模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<OwnerImport> util = new ExcelUtil<OwnerImport>(OwnerImport.class);
        return util.importTemplateExcel("业主数据");
    }


    @Override
    protected void updateOwnerParkingInfo(String ownerId) {
        super.updateOwnerParkingInfo(ownerId);
    }
}
