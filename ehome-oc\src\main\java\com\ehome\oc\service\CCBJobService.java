package com.ehome.oc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.LoggerUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.common.utils.xml.JsonToXmlUtils;
import com.ehome.common.utils.xml.XmlToJsonUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
public class CCBJobService {
    private static final String CCB_LOGGER_NAME = "ccb-request";
    private final Logger logger = LoggerUtils.getLog(CCB_LOGGER_NAME);
    
    private static final String CUST_ID = "HA41000009262264901";
    private static final String USER_ID = "WLPT01";
    private static final String PASSWORD = "wkk79310";
    private static final String ACC_NO = "41050180380300001715";
    private static final String LANGUAGE = "CN";

    public void synCardTransDetailList(){
        String endDate = getDateBeforeDays(0);
        synCardTransDetailList(null, endDate);
    }

    public static String getDateBeforeDays(int daysAgo) {
        LocalDate targetDate = LocalDate.now().minusDays(daysAgo);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return targetDate.format(formatter);
    }

    public void synCardTransDetailListByMonth(){
        String beginDate =  DateUtils.dateTimeNow("YYYYMM")+"01";
        String endDate = DateUtils.dateTimeNow("YYYYMM")+"31";
        synCardTransDetailList(beginDate, endDate);
    }

    public void synCardTransDetailList(String date) {
        synCardTransDetailList(date,date);
    }

    public int synCardTransDetailList(String beginDate, String endDate) {
        return  synCardTransDetailList(null,beginDate,endDate);
    }
    /**
     * 同步银行卡交易明细记录
     */
    public int synCardTransDetailList(String communityId,String beginDate, String endDate) {
        EasySQL sql = new EasySQL();
        sql.append("CCB","select * from eh_pms_bank_account where bank_type = ? and status = 0");
        sql.append(communityId,"and community_id = ?");
        List<Record> bankAccount = Db.find(sql.getSQL(),sql.getParams());
        int rowRecord = 0;
        if(bankAccount == null){
            logger.error("同步银行卡交易明细记录失败，银行账号不存在");
            return rowRecord;
        }
        if(beginDate==null){
             beginDate =  getDateBeforeDays(31);
        }
        for(Record bankInfo : bankAccount){
            BigDecimal amtAll  = new BigDecimal(0);
            int bankTranRecordSum = 0;
            try {
                String lastGetdataDate=  bankInfo.getStr("last_getdata_date");
                if(StringUtils.isNotEmpty(lastGetdataDate)){
                    beginDate = lastGetdataDate;
                }
                JSONObject txInfoJson = new JSONObject();
                txInfoJson.put("ACCNO1", bankInfo.getStr("acc_no"));
                txInfoJson.put("STARTDATE", beginDate);
                txInfoJson.put("ENDDATE", endDate);
                txInfoJson.put("CURR_COD", 156);
                txInfoJson.put("CSHEX_CD", 1);
                txInfoJson.put("PAGE", 1);
                txInfoJson.put("TOTAL_RECORD", 500);

                JSONObject txJson = new JSONObject();
                txJson.put("TX_INFO", txInfoJson);
                txJson.put("REQUEST_SN", System.currentTimeMillis());
                txJson.put("CUST_ID", CUST_ID);
                txJson.put("USER_ID", USER_ID);
                txJson.put("PASSWORD", PASSWORD);
                txJson.put("LANGUAGE", LANGUAGE);
                //6W0101-账户信息查询
                txJson.put("TX_CODE", "6WY101");

                String requestXml = JsonToXmlUtils.jsonToXmlStr(txJson);
                String result = xmlRequest(bankInfo.getStr("api_url"),requestXml);

                logger.info("同步银行卡交易明细记录 - Request XML: {}", requestXml);
                logger.info("同步银行卡交易明细记录 - Response result: {}", result);

                // 解析返回数据
                JSONObject jsonObject =  XmlToJsonUtils.xmltoJson(result);
                logger.info("同步银行卡交易明细记录 - Response.toJSONString: {}", jsonObject.toJSONString());
                JSONObject DETAILLIST =  jsonObject.getJSONObject("TX").getJSONObject("TX_INFO").getJSONObject("DETAILLIST");
                JSONArray array = null;
                if(DETAILLIST!=null){
                    if(DETAILLIST.get("DETAILINFO") instanceof  JSONArray){
                        array = DETAILLIST.getJSONArray("DETAILINFO");
                    }else{
                        array = new JSONArray();
                        array.add(DETAILLIST.getJSONObject("DETAILINFO"));
                    }
                }else{
                    array = new JSONArray();
                }
                logger.info("同步银行卡交易明细记录 - Response.DETAILLIST: {},数量：{}", array,array.size());
                for(int i=0;i<array.size();i++){
                    JSONObject row = array.getJSONObject(i);
                    logger.info("同步银行卡交易明细记录 - Response.DETAILLIST.DETAILINFO: {}", row);

                    Record record = new Record();
                    String no = row.getString("Ovrlsttn_Trck_No");
                    if(StringUtils.isEmpty(no)){
                        no = Seq.getId("tran");
                        continue;
                    }
                    record.set("trck_no",no);
                    record.set("pms_id",bankInfo.getStr("pms_id"));
                    record.set("community_id",bankInfo.getStr("community_id"));
                    record.set("amt",row.getBigDecimal("AMT"));
                    amtAll = row.getBigDecimal("AMT1");
                    record.set("amt_all",row.getBigDecimal("AMT1"));
                    record.set("tran_date",row.getString("TRANDATE"));
                    record.set("tran_date_id",row.getString("TRANDATE").replace("/",""));
                    record.set("tran_month",row.getString("TRANDATE").replace("/","").substring(0,6));
                    record.set("tran_time",row.getString("TRANTIME"));
                    record.set("tran_datetime",row.getString("TRANDATE")+" "+row.getString("TRANTIME"));
                    record.set("acct_no",row.getString("ACCNO2"));//客户账号
                    record.set("cust_bank_name",row.getString("CADBank_Nm"));//客户银行
                    record.set("tran_flow_no",row.getString("TRAN_FLOW"));//交易流水号
                    record.set("bank_no",bankInfo.getStr("acc_no"));//小区账号
                    record.set("acct_name",row.getString("ACC_NAME1"));
                    record.set("message",row.getString("MESSAGE"));
                    record.set("tran_remark",row.getString("DET"));
                    record.set("remark",row.getString("DET"));
                    record.set("real_tran_date",row.getString("REAL_TRANDATE"));
                    String tranType = row.getString("FLAG1");
                    if("1".equals(tranType)){
                        record.set("direction","in");
                    }else if("0".equals(tranType)){
                        record.set("direction","out");
                    }
                    record.set("status",bankInfo.getStr("publish_status"));
                    int  count = Db.queryInt("select count(1) from eh_tran_record where trck_no = ? ",record.getStr("trck_no"));
                    if(count==0){
                        Db.save("eh_tran_record","trck_no",record);
                        rowRecord = rowRecord + 1;
                        bankTranRecordSum = bankTranRecordSum + 1;
                        logger.info("同步银行卡交易明细记录成功 - 保存交易记录: {}", record.toMap());
                    }
                }

                } catch (Exception e) {
                    logger.error("同步银行卡交易明细记录失败", e);
            }
            if(bankTranRecordSum>0){
                Record record = new Record();
                record.set("id",bankInfo.getStr("id"));
                record.set("last_getdata_time",DateUtils.getTime());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                record.set("last_getdata_date",formatter.format(new Date()));
                if(amtAll.compareTo(BigDecimal.ZERO) > 0){
                    record.set("balance",amtAll);
                    record.set("balance_last_time",formatter.format(new Date()));
                }
                Db.update("eh_pms_bank_account", "id", record);
            }
        }
        return rowRecord;
    }

    public int syncBalance() {
        List<Record> bankAccount = Db.find("select * from eh_pms_bank_account where bank_type = ? and status = 0", "CCB");
        int rowRecord = 0;
        if(bankAccount == null){
            logger.error("同步银行卡余额记录失败，银行账号不存在");
            return rowRecord;
        }
        for(Record bankInfo : bankAccount){
            try {
                JSONObject txInfoJson = new JSONObject();
                txInfoJson.put("ACC_NO", bankInfo.getStr("acc_no"));
                txInfoJson.put("CURR_COD", 156);

                JSONObject txJson = new JSONObject();
                txJson.put("TX_INFO", txInfoJson);
                txJson.put("REQUEST_SN", System.currentTimeMillis());
                txJson.put("CUST_ID", CUST_ID);
                txJson.put("USER_ID", USER_ID);
                txJson.put("PASSWORD", PASSWORD);
                txJson.put("LANGUAGE", LANGUAGE);
                //6W0101-账户信息查询
                txJson.put("TX_CODE", "6W0100");

                String requestXml = JsonToXmlUtils.jsonToXmlStr(txJson);
                String result = xmlRequest(bankInfo.getStr("api_url"),requestXml);

                logger.info("同步银行卡余额记录 - Request XML: {}", requestXml);
                logger.info("同步银行卡余额记录 - Response result: {}", result);

                // 解析返回数据
                JSONObject jsonObject =  XmlToJsonUtils.xmltoJson(result);
                logger.info("同步银行卡余额记录 - Response.toJSONString: {}", jsonObject.toJSONString());

                if(result.contains("权限")){
                    logger.error("同步银行卡余额记录失败，返回结果: {}", result);
                    continue;
                }
                Record record = new Record();
                record.set("id",bankInfo.getStr("id"));
                record.set("balance",jsonObject.getJSONObject("TX").getJSONObject("TX_INFO").getString("BALANCE"));
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                record.set("balance_last_time",formatter.format(new Date()));
                Db.update("eh_pms_bank_account", "id", record);
            } catch (Exception e) {
                logger.error("同步银行卡余额记录失败", e);
            }
        }
        return rowRecord;
    }


    private String xmlRequest(String xmlContent) {
        String path = "http://172.16.66.14:12345";
        return xmlRequest(path,xmlContent);
    }

    private String xmlRequest(String path, String sRequest) {
        OutputStream out = null;
        BufferedReader in = null;
        HttpURLConnection conn = null;
        
        try {
            String requestXml = "<?xml version=\"1.0\" encoding=\"GB2312\" standalone=\"yes\" ?>" + sRequest;
            String encoding = "GB18030";
            String params = "requestXml=" + sRequest;
            conn = getHttpURLConnection(path, params);
            
            // 发送请求报文数据
            out = conn.getOutputStream();
            out.write(params.getBytes(encoding));
            out.flush();
            
            // 读取返回数据
            if (conn.getResponseCode() == 200) {
                in = new BufferedReader(new InputStreamReader(conn.getInputStream(), encoding));
            } else {
                in = new BufferedReader(new InputStreamReader(conn.getErrorStream(), encoding));
            }
            
            StringBuilder sb = new StringBuilder();
            String sLine;
            while ((sLine = in.readLine()) != null) {
                sb.append(sLine);
            }
            
            return sb.toString();
            
        } catch (Exception e) {
            logger.error("XML请求失败: {}", e.getMessage());
            return null;
        } finally {
            // 关闭资源
            try {
                if (out != null) out.close();
                if (in != null) in.close();
                if (conn != null) conn.disconnect();
            } catch (IOException e) {
                logger.error("关闭资源失败: {}", e.getMessage());
            }
        }
    }

    private HttpURLConnection getHttpURLConnection(String path, String params) throws IOException {
        URL url = new URL(path);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(10 * 1000);
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        conn.setRequestProperty("Content-Length", String.valueOf(params.length()));
        conn.setRequestProperty("Connection", "close");
        return conn;
    }
}
