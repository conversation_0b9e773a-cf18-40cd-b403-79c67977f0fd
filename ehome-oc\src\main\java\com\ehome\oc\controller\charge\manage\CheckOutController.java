package com.ehome.oc.controller.charge.manage;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.oc.service.charge.ChargeBillService;
import com.ehome.oc.service.charge.ChargeCommonService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/oc/charge/manage/checkout")
public class CheckOutController extends BaseController {

    private static final String PREFIX = "oc/charge/manage/checkout";

    @Autowired
    private ChargeBillService chargeBillService;

    @Autowired
    private ChargeCommonService chargeCommonService;

    /**
     * 收银台主页面
     */
    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    /**
     * 获取楼栋下的房屋缴费情况（按单元分组）
     */
    @PostMapping("/getBuildingHouseList")
    @ResponseBody
    public AjaxResult getBuildingHouseList() {
        JSONObject params = getParams();
        String buildingId = params.getString("buildingId");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(buildingId, "楼栋ID");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            // 查询楼栋下的所有单元
            List<Record> unitList = Db.find(
                "SELECT unit_id, name as unit_name FROM eh_unit WHERE building_id = ? ORDER BY name",
                buildingId);

            List<Map<String, Object>> unitDataList = new ArrayList<>();
            int totalHouseCount = 0;
            int totalArrearageHouseCount = 0;

            for (Record unit : unitList) {
                String unitId = unit.getStr("unit_id");
                String unitName = unit.getStr("unit_name");

                // 查询单元下的房屋信息
                List<Record> houseList = Db.find(
                    "SELECT h.house_id, h.room, h.combina_name, h.house_status, h.arrear_amount, " +
                    "h.owner_str, h.owner_count, " +
                    "CASE WHEN h.arrear_amount > 0 THEN 1 ELSE 0 END as is_arrearage " +
                    "FROM eh_house_info h " +
                    "WHERE h.unit_id = ? " +
                    "ORDER BY h.room",
                    unitId);

                int unitHouseCount = houseList.size();
                int unitArrearageHouseCount = 0;

                for (Record house : houseList) {
                    if (house.getBigDecimal("arrear_amount").compareTo(java.math.BigDecimal.ZERO) > 0) {
                        unitArrearageHouseCount++;
                    }
                }

                Map<String, Object> unitData = new HashMap<>();
                unitData.put("unitId", unitId);
                unitData.put("unitName", unitName.endsWith("单元") ? unitName : unitName + "单元");
                unitData.put("houseList", convertRecordToMap(houseList));
                unitData.put("totalHouseCount", unitHouseCount);
                unitData.put("arrearageHouseCount", unitArrearageHouseCount);

                unitDataList.add(unitData);
                totalHouseCount += unitHouseCount;
                totalArrearageHouseCount += unitArrearageHouseCount;
            }

            // 构造返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("unitList", unitDataList);
            result.put("totalHouseCount", totalHouseCount);
            result.put("totalArrearageHouseCount", totalArrearageHouseCount);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取楼栋房屋列表失败", e);
            return AjaxResult.error("获取房屋列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取单元下的房屋缴费情况
     */
    @PostMapping("/getUnitHouseList")
    @ResponseBody
    public TableDataInfo getUnitHouseList() {
        JSONObject params = getParams();

        try {
            // 构建查询SQL
            EasySQL sql = buildHouseListQuery(params);

            // 使用分页查询
            Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "SELECT h.house_id, h.room, h.combina_name, h.house_status, h.arrear_amount, " +
                "h.owner_str, h.owner_count, h.create_time, " +
                "CASE WHEN h.arrear_amount > 0 THEN 1 ELSE 0 END as is_arrearage",
                sql.toFullSql()
            );

            // 如果是单元查询，返回统计信息用于显示
            String unitId = params.getString("unitId");
            if (StringUtils.isNotEmpty(unitId)) {
                // 查询统计信息（不分页）
                StringBuilder statsSql = new StringBuilder();
                List<Object> statsParams = new ArrayList<>();

                statsSql.append("SELECT COUNT(*) as total_count, ");
                statsSql.append("SUM(CASE WHEN h.arrear_amount > 0 THEN 1 ELSE 0 END) as arrear_count ");
                statsSql.append("FROM eh_house_info h WHERE h.unit_id = ? ");
                statsParams.add(unitId);

                // 添加相同的搜索条件
                String searchKeyword = params.getString("searchKeyword");
                if (StringUtils.isNotEmpty(searchKeyword)) {
                    statsSql.append("AND (h.combina_name LIKE ? OR h.room LIKE ?) ");
                    statsParams.add("%" + searchKeyword + "%");
                    statsParams.add("%" + searchKeyword + "%");
                }

                String room = params.getString("room");
                if (StringUtils.isNotEmpty(room)) {
                    statsSql.append("AND h.room LIKE ? ");
                    statsParams.add("%" + room + "%");
                }

                String arrearStatus = params.getString("arrear_status");
                if (StringUtils.isNotEmpty(arrearStatus)) {
                    if ("1".equals(arrearStatus)) {
                        statsSql.append("AND h.arrear_amount > 0 ");
                    } else if ("0".equals(arrearStatus)) {
                        statsSql.append("AND h.arrear_amount = 0 ");
                    }
                }

                Record statsRecord = Db.findFirst(statsSql.toString(), statsParams.toArray());
                int totalHouseCount = statsRecord != null ? statsRecord.getInt("total_count") : 0;
                int arrearageHouseCount = statsRecord != null ? statsRecord.getInt("arrear_count") : 0;

                // 将统计信息添加到响应中
                TableDataInfo dataTable = getDataTable(paginate);
                Map<String, Object> extraData = new HashMap<>();
                extraData.put("totalHouseCount", totalHouseCount);
                extraData.put("arrearageHouseCount", arrearageHouseCount);
                dataTable.setMsg(JSONObject.toJSONString(extraData));
                return dataTable;
            }

            return getDataTable(paginate);
        } catch (Exception e) {
            logger.error("获取单元房屋列表失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 获取房屋详情
     */
    @PostMapping("/getHouseDetail")
    @ResponseBody
    public AjaxResult getHouseDetail() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");

        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }

        try {
            // 查询房屋详细信息
            Record house = Db.findFirst(
                "SELECT h.*, b.name as building_name, u.name as unit_name " +
                "FROM eh_house_info h " +
                "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                "WHERE h.house_id = ?",
                houseId);

            if (house == null) {
                return AjaxResult.error("房屋不存在");
            }

            return AjaxResult.success(house.toMap());
        } catch (Exception e) {
            logger.error("获取房屋详情失败", e);
            return AjaxResult.error("获取房屋详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取房屋的未缴账单列表
     */
    @PostMapping("/getHouseBills")
    @ResponseBody
    public TableDataInfo getHouseBills() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        return getHouseBillsByPayStatus(params, houseId, 0, "未缴");
    }

    /**
     * 获取房屋已缴账单列表
     */
    @PostMapping("/getPaidHouseBills")
    @ResponseBody
    public TableDataInfo getPaidHouseBills() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        return getHouseBillsByPayStatus(params, houseId, 1, "已缴");
    }

    /**
     * 通用的房屋账单查询方法 - 优化版本，减少代码重复
     */
    private TableDataInfo getHouseBillsByPayStatus(JSONObject params, String houseId, int payStatus, String payStatusStr) {
        if (StringUtils.isEmpty(houseId)) {
            return getDataTable(new ArrayList<>());
        }

        try {
            // 验证houseId格式
            Long houseIdLong = validateAndParseHouseId(houseId);
            if (houseIdLong == null) {
                logger.warn("房屋ID格式错误: {}", houseId);
                return getDataTable(new ArrayList<>());
            }

            // 构建查询SQL
            EasySQL sql = buildHouseBillQuery(houseIdLong, payStatus);

            // 选择字段
            String selectFields = buildBillSelectFields(payStatus == 1);

            // 使用分页查询
            Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                selectFields,
                sql.toFullSql()
            );

            // 处理显示字段
            processBillRecords(paginate.getList(), payStatusStr);

            return getDataTable(paginate);
        } catch (Exception e) {
            logger.error("获取房屋账单失败，房屋ID：{}，支付状态：{}", houseId, payStatus, e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 房屋详情页面
     */
    @GetMapping("/detail/{houseId}")
    public String detail(@PathVariable("houseId") String houseId, ModelMap mmap) {
        mmap.put("houseId", houseId);
        return PREFIX + "/detail";
    }

    /**
     * 批量更新房屋欠费金额
     */
    @PostMapping("/updateArrearAmount")
    @ResponseBody
    public AjaxResult updateArrearAmount() {
        try {
            String communityId = getSysUser().getCommunityId();
            int updateCount = chargeBillService.updateAllHouseArrearAmount(communityId);
            return AjaxResult.success("成功更新 " + updateCount + " 套房屋的欠费金额");
        } catch (Exception e) {
            logger.error("批量更新房屋欠费金额失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 将Record列表转换为Map列表
     */
    private List<Map<String, Object>> convertRecordToMap(List<Record> records) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (records != null) {
            for (Record record : records) {
                result.add(record.toMap());
            }
        }
        return result;
    }

    /**
     * 解析账单ID字符串为Long列表
     */
    private List<Long> parseBillIds(String billIdsStr) throws NumberFormatException {
        List<Long> billIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(billIdsStr)) {
            String[] billIdArray = billIdsStr.split(",");
            for (String billIdStr : billIdArray) {
                if (StringUtils.isNotEmpty(billIdStr.trim())) {
                    billIds.add(Long.parseLong(billIdStr.trim()));
                }
            }
        }
        return billIds;
    }

    /**
     * 批量查询未缴费账单 - 优化版本，支持大数据量分页处理
     */
    private List<Record> getUnpaidBillsByIds(List<Long> billIds) {
        if (billIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 分页处理大数据量，避免IN查询限制
        final int BATCH_SIZE = 500; // 每批处理500个ID
        List<Record> allResults = new ArrayList<>();

        for (int i = 0; i < billIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, billIds.size());
            List<Long> batchIds = billIds.subList(i, endIndex);

            // 构建当前批次的IN查询条件
            String inClause = batchIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

            List<Record> batchResults = Db.find(
                "SELECT id, asset_type, asset_id, pay_status FROM eh_charge_bill " +
                "WHERE id IN (" + inClause + ") AND pay_status = 0 AND is_bad_bill = 0"
            );

            allResults.addAll(batchResults);
        }

        return allResults;
    }

    /**
     * 批量更新账单支付状态 - 优化版本，支持大数据量分页处理
     */
    private int batchUpdateBillPaymentStatus(List<Record> bills, Integer payType) {
        if (bills.isEmpty()) {
            return 0;
        }

        String currentTime = com.ehome.common.utils.DateUtils.parseDateToStr(
            com.ehome.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS, new java.util.Date());
        String userName = getSysUser().getUserName();

        // 提取账单ID列表
        List<Long> billIds = bills.stream()
            .map(bill -> bill.getLong("id"))
            .collect(Collectors.toList());

        // 分页处理大数据量更新
        final int BATCH_SIZE = 500; // 每批处理500个ID
        int totalUpdateCount = 0;

        for (int i = 0; i < billIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, billIds.size());
            List<Long> batchIds = billIds.subList(i, endIndex);

            // 构建当前批次的IN查询条件
            String inClause = batchIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

            // 执行当前批次的批量更新
            int batchUpdateCount = Db.update(
                "UPDATE eh_charge_bill SET " +
                "pay_status = 1, " +
                "pay_type = ?, " +
                "pay_time = ?, " +
                "update_time = ?, " +
                "update_by = ? " +
                "WHERE id IN (" + inClause + ") AND pay_status = 0",
                payType, currentTime, currentTime, userName
            );

            totalUpdateCount += batchUpdateCount;

            // 记录批次处理日志
            logger.debug("批量更新账单支付状态，批次：{}/{}, 更新数量：{}",
                        (i / BATCH_SIZE + 1), (billIds.size() + BATCH_SIZE - 1) / BATCH_SIZE, batchUpdateCount);
        }

        return totalUpdateCount;
    }

    /**
     * 批量更新房屋欠费金额
     */
    private void updateHouseArrearAmountForBills(List<Record> bills) {
        // 提取房屋ID列表
        List<Long> houseIds = bills.stream()
            .filter(bill -> bill.getInt("asset_type") == 1) // 只处理房屋类型
            .map(bill -> bill.getLong("asset_id"))
            .distinct()
            .collect(Collectors.toList());

        if (!houseIds.isEmpty()) {
            // 批量更新房屋欠费金额
            for (Long houseId : houseIds) {
                chargeBillService.updateHouseArrearAmount(houseId);
            }
        }
    }

    /**
     * 构建房屋列表查询SQL
     */
    private EasySQL buildHouseListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("FROM eh_house_info h");
        sql.append("WHERE 1=1");
        sql.append(getSysUser().getCommunityId(), "AND h.community_id = ?", false);

        // 单元条件
        sql.append(params.getString("unitId"), "AND h.unit_id = ?");

        // 楼栋条件
        sql.append(params.getString("buildingId"), "AND h.building_id = ?");

        // 搜索条件
        String searchKeyword = params.getString("searchKeyword");
        if (StringUtils.isNotEmpty(searchKeyword)) {
            sql.appendLike(searchKeyword, "AND (h.combina_name LIKE ? OR h.room LIKE ?)");
        }

        // 房间号条件
        sql.appendLike(params.getString("room"), "AND h.room LIKE ?");

        // 欠费状态条件
        String arrearStatus = params.getString("arrear_status");
        if (StringUtils.isNotEmpty(arrearStatus)) {
            if ("1".equals(arrearStatus)) {
                sql.append("AND h.arrear_amount > 0");
            } else if ("0".equals(arrearStatus)) {
                sql.append("AND h.arrear_amount = 0");
            }
        }

        sql.append("ORDER BY h.room");
        return sql;
    }

    /**
     * 批量收款 - 优化版本，解决N+1查询问题
     */
    @PostMapping("/batchPayment")
    @ResponseBody
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult batchPayment() {
        JSONObject params = getParams();
        String billIdsStr = params.getString("billIds");
        Integer payType = params.getInteger("payType");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateBillIds = chargeCommonService.validateBillIds(billIdsStr);
        if (validateBillIds != null) {
            return validateBillIds;
        }

        AjaxResult validatePayType = chargeCommonService.validatePayType(payType);
        if (validatePayType != null) {
            return validatePayType;
        }

        try {
            // 解析账单ID列表
            List<Long> billIds = parseBillIds(billIdsStr);
            if (billIds.isEmpty()) {
                return AjaxResult.error("账单ID格式错误");
            }

            // 批量查询未缴费账单
            List<Record> unpaidBills = getUnpaidBillsByIds(billIds);
            if (unpaidBills.isEmpty()) {
                return AjaxResult.error("没有可收款的账单");
            }

            // 批量更新账单状态
            int updateCount = batchUpdateBillPaymentStatus(unpaidBills, payType);

            // 批量更新房屋欠费金额
            updateHouseArrearAmountForBills(unpaidBills);

            logger.info("批量收款成功，更新{}条账单，操作人：{}", updateCount, getSysUser().getUserName());
            return AjaxResult.success("成功收款 " + updateCount + " 条账单");

        } catch (NumberFormatException e) {
            logger.error("账单ID格式错误", e);
            return AjaxResult.error("账单ID格式错误");
        } catch (Exception e) {
            logger.error("批量收款失败", e);
            return AjaxResult.error("批量收款失败：" + e.getMessage());
        }
    }

    /**
     * 修改账单备注 - 优化版本，增强参数验证和性能
     */
    @PostMapping("/updateRemark")
    @ResponseBody
    public AjaxResult updateRemark() {
        JSONObject params = getParams();
        String billId = params.getString("billId");
        String remark = params.getString("remark");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(billId, "账单ID");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            // 验证账单ID格式
            Long billIdLong = Long.parseLong(billId.trim());

            // 直接更新，避免先查询再更新
            String currentTime = com.ehome.common.utils.DateUtils.parseDateToStr(
                com.ehome.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS, new java.util.Date());

            int updateCount = Db.update(
                "UPDATE eh_charge_bill SET remark = ?, update_time = ?, update_by = ? WHERE id = ?",
                remark, currentTime, getSysUser().getUserName(), billIdLong);

            if (updateCount == 0) {
                return AjaxResult.error("账单不存在或更新失败");
            }

            logger.info("账单备注修改成功，账单ID：{}，操作人：{}", billIdLong, getSysUser().getUserName());
            return AjaxResult.success("备注修改成功");
        } catch (NumberFormatException e) {
            logger.warn("账单ID格式错误: {}", billId);
            return AjaxResult.error("账单ID格式错误");
        } catch (Exception e) {
            logger.error("修改备注失败，账单ID：{}", billId, e);
            return AjaxResult.error("修改备注失败：" + e.getMessage());
        }
    }

    /**
     * 作废账单 - 优化版本，增强参数验证和业务逻辑
     */
    @PostMapping("/voidBill")
    @ResponseBody
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult voidBill() {
        JSONObject params = getParams();
        String billId = params.getString("billId");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(billId, "账单ID");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            // 验证账单ID格式
            Long billIdLong = Long.parseLong(billId.trim());

            // 查询账单状态
            Record bill = Db.findFirst("SELECT pay_status, asset_type, asset_id FROM eh_charge_bill WHERE id = ?", billIdLong);
            if (bill == null) {
                return AjaxResult.error("账单不存在");
            }

            if (bill.getInt("pay_status") == 1) {
                return AjaxResult.error("已缴费账单不能作废");
            }

            // 标记为坏账
            String currentTime = com.ehome.common.utils.DateUtils.parseDateToStr(
                com.ehome.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS, new java.util.Date());

            int updateCount = Db.update(
                "UPDATE eh_charge_bill SET is_bad_bill = 1, bad_bill_state = 1, update_time = ?, update_by = ? WHERE id = ? AND pay_status = 0",
                currentTime, getSysUser().getUserName(), billIdLong);

            if (updateCount == 0) {
                return AjaxResult.error("账单作废失败，可能已被其他人处理");
            }

            // 如果是房屋账单，更新房屋欠费金额
            if (bill.getInt("asset_type") == 1) {
                chargeBillService.updateHouseArrearAmount(bill.getLong("asset_id"));
            }

            logger.info("账单作废成功，账单ID：{}，操作人：{}", billIdLong, getSysUser().getUserName());
            return AjaxResult.success("账单作废成功");
        } catch (NumberFormatException e) {
            logger.warn("账单ID格式错误: {}", billId);
            return AjaxResult.error("账单ID格式错误");
        } catch (Exception e) {
            logger.error("作废账单失败，账单ID：{}", billId, e);
            return AjaxResult.error("作废账单失败：" + e.getMessage());
        }
    }

    /**
     * 获取账单详情 - 优化版本，增强参数验证
     */
    @PostMapping("/getBillDetail")
    @ResponseBody
    public AjaxResult getBillDetail() {
        JSONObject params = getParams();
        String billId = params.getString("billId");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(billId, "账单ID");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            // 验证账单ID格式
            Long billIdLong = Long.parseLong(billId.trim());

            Record bill = chargeCommonService.getBillDetail(billIdLong);
            if (bill == null) {
                return chargeCommonService.buildErrorResponse("账单不存在");
            }

            return chargeCommonService.buildSuccessResponse("获取成功", bill.toMap());
        } catch (NumberFormatException e) {
            logger.warn("账单ID格式错误: {}", billId);
            return chargeCommonService.buildErrorResponse("账单ID格式错误");
        } catch (Exception e) {
            logger.error("获取账单详情失败，账单ID：{}", billId, e);
            return chargeCommonService.buildErrorResponse("获取账单详情失败", e);
        }
    }

    /**
     * 验证并解析房屋ID
     */
    private Long validateAndParseHouseId(String houseId) {
        try {
            return StringUtils.isNotEmpty(houseId) ? Long.parseLong(houseId.trim()) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 构建房屋账单查询SQL
     */
    private EasySQL buildHouseBillQuery(Long houseId, int payStatus) {
        EasySQL sql = new EasySQL();
        sql.append("FROM eh_charge_bill cb");
        sql.append("LEFT JOIN eh_charge_standard cs ON cb.charge_standard_id = cs.id");
        sql.append("LEFT JOIN eh_house_info h ON cb.asset_id = h.house_id AND cb.asset_type = 1");

        // 基础条件
        sql.append(houseId, "WHERE cb.asset_type = 1 AND cb.asset_id = ?", false);
        sql.append(payStatus, "AND cb.pay_status = ?", false);

        // 未缴费账单需要排除坏账
        if (payStatus == 0) {
            sql.append("AND cb.is_bad_bill = 0");
        }

        // 排序
        if (payStatus == 0) {
            sql.append("ORDER BY cb.in_month DESC");
        } else {
            sql.append("ORDER BY cb.create_time DESC");
        }

        return sql;
    }

    /**
     * 构建账单查询字段
     */
    private String buildBillSelectFields(boolean includePaidTime) {
        StringBuilder fields = new StringBuilder();
        fields.append("SELECT cb.*, cs.name as charge_standard_name, h.combina_name, ");
        fields.append("cb.amount, cb.discount_amount, cb.late_money_amount, cb.bill_amount, ");
        fields.append("cb.start_time, cb.end_time, cb.pay_type, cb.create_time, cb.remark");

        if (includePaidTime) {
            fields.append(", cb.pay_time");
        }

        return fields.toString();
    }

    /**
     * 处理账单记录显示字段
     */
    private void processBillRecords(List<Record> bills, String payStatusStr) {
        for (Record bill : bills) {
            bill.set("pay_status_str", payStatusStr);
        }
    }
}
