-- `eh_account_type`
CREATE TABLE `eh_account_type` (
                                   `id` varchar(32) NOT NULL COMMENT '主键ID',
                                   `name` varchar(100) NOT NULL COMMENT '分类名称',
                                   `direction` varchar(10) NOT NULL DEFAULT 'in' COMMENT '收支方向：in收入，out支出',
                                   `parent_id` varchar(32) DEFAULT NULL COMMENT '父级ID，支持分类层级',
                                   `community_id` varchar(32) DEFAULT '' COMMENT '所属小区，可为空表示全局通用',
                                   `status` varchar(10) DEFAULT 'enabled' COMMENT '状态：enabled启用，disabled停用',
                                   `remark` varchar(255) DEFAULT '' COMMENT '备注说明',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账目分类表';

-- `eh_attachment_relation`
CREATE TABLE `eh_attachment_relation` (
                                          `relation_id` varchar(32) NOT NULL COMMENT '关联ID',
                                          `business_type` varchar(50) NOT NULL COMMENT '业务类型(notice-公告,menu-菜单,etc)',
                                          `business_id` varchar(32) NOT NULL COMMENT '业务ID',
                                          `file_id` varchar(32) NOT NULL COMMENT '文件ID',
                                          `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
                                          `create_time` datetime NOT NULL COMMENT '创建时间',
                                          `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                          `community_id` varchar(32) DEFAULT '0' COMMENT '小区ID',
                                          `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1删除)',
                                          PRIMARY KEY (`relation_id`),
                                          KEY `idx_business` (`business_type`,`business_id`),
                                          KEY `idx_file_id` (`file_id`),
                                          KEY `idx_community` (`community_id`),
                                          KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附件关联表';

-- `eh_building`
CREATE TABLE `eh_building` (
                               `building_id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '楼栋ID',
                               `community_id` varchar(32) NOT NULL COMMENT '小区ID',
                               `community_name` varchar(100) DEFAULT '' COMMENT '小区名称',
                               `name` varchar(50) NOT NULL COMMENT '楼栋名称或编号',
                               `alias_name` varchar(100) DEFAULT '' COMMENT '楼栋别称',
                               `total_units` int(11) DEFAULT '0' COMMENT '单元总数',
                               `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                               `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
                               `update_by` varchar(30) DEFAULT NULL COMMENT '更新人呢',
                               `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
                               `remark` varchar(255) DEFAULT '' COMMENT '备注',
                               `manager` varchar(50) DEFAULT '' COMMENT '楼栋管家',
                               `house_count` int(11) DEFAULT '0' COMMENT '户数',
                               `house_area` decimal(10,2) DEFAULT '0.00' COMMENT '房屋面积',
                               `order_index` int(11) DEFAULT '100' COMMENT '排序',
                               PRIMARY KEY (`building_id`)
) ENGINE=InnoDB AUTO_INCREMENT=56145 DEFAULT CHARSET=utf8mb4 COMMENT='楼栋信息表';

-- `eh_charge_bill`
CREATE TABLE `eh_charge_bill` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
                                  `community_id` varchar(32) NOT NULL COMMENT '小区ID',
                                  `asset_type` tinyint(4) NOT NULL COMMENT '资产类型(1:房屋,2:车位,3:商铺)',
                                  `asset_id` bigint(20) NOT NULL COMMENT '资产ID',
                                  `asset_name` varchar(200) NOT NULL COMMENT '资产名称',
                                  `charge_standard_id` bigint(20) NOT NULL COMMENT '收费标准ID',
                                  `charge_standard_version` int(11) NOT NULL DEFAULT '1' COMMENT '收费标准版本号',
                                  `charge_binding_id` bigint(20) NOT NULL COMMENT '收费绑定ID',
                                  `charge_item_name` varchar(100) NOT NULL COMMENT '收费项目名称',
                                  `charge_item_type` tinyint(4) NOT NULL COMMENT '收费类型(1:周期性收费,2:走表收费,3:临时性收费)',
                                  `start_time` int(11) DEFAULT '0' COMMENT '计费开始日期(yyyyMMdd)',
                                  `end_time` int(11) DEFAULT '0' COMMENT '计费结束日期(yyyyMMdd)',
                                  `in_month` varchar(7) NOT NULL COMMENT '账期月份(YYYY-MM)',
                                  `bill_type` tinyint(4) NOT NULL DEFAULT '3' COMMENT '账单类型(1:手工账单,2:补缴账单,3:系统生成)',
                                  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账单金额（元）',
                                  `bill_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '应收金额（元）账单金额-优惠+违约金额',
                                  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额（元）',
                                  `late_money_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '滞纳金金额（元）',
                                  `deposit_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '押金金额（元）',
                                  `pay_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '支付状态(0:未缴,1:已缴,2:部分缴费)',
                                  `pay_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '支付方式(0:未支付,1:现金,2:微信,3:支付宝,4:银行转账)',
                                  `pay_time` varchar(19) DEFAULT '' COMMENT '缴费日期',
                                  `second_pay_channel` tinyint(4) NOT NULL DEFAULT '0' COMMENT '第二支付渠道',
                                  `second_pay_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '第二支付金额（元）',
                                  `can_revoke` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否可撤销(0:否,1:是)',
                                  `bad_bill_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '坏账状态(0:正常,1:坏账)',
                                  `is_bad_bill` int(11) NOT NULL DEFAULT '0' COMMENT '是否坏账',
                                  `has_split` int(11) NOT NULL DEFAULT '0' COMMENT '是否拆分账单',
                                  `split_desc` varchar(500) DEFAULT NULL COMMENT '拆分说明',
                                  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
                                  `deal_log_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '处理日志ID',
                                  `receipt_id` varchar(50) DEFAULT NULL COMMENT '收据ID',
                                  `create_time` varchar(19) DEFAULT '' COMMENT '账单生成日期',
                                  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                  `update_time` varchar(19) DEFAULT '' COMMENT '更新时间',
                                  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
                                  `last_op_time` varchar(19) DEFAULT '' COMMENT '最后操作时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `idx_community_asset` (`community_id`,`asset_type`,`asset_id`) USING BTREE,
                                  KEY `idx_charge_standard` (`charge_standard_id`) USING BTREE,
                                  KEY `idx_charge_binding` (`charge_binding_id`) USING BTREE,
                                  KEY `idx_in_month` (`in_month`) USING BTREE,
                                  KEY `idx_pay_status` (`pay_status`) USING BTREE,
                                  KEY `idx_create_time` (`create_time`) USING BTREE,
                                  KEY `idx_standard_version` (`charge_standard_id`,`charge_standard_version`) USING BTREE,
                                  KEY `idx_charge_bill_amount` (`amount`),
                                  KEY `idx_charge_bill_pay_status_amount` (`pay_status`,`amount`),
                                  KEY `idx_charge_bill_create_time` (`create_time`),
                                  KEY `idx_charge_bill_binding_month` (`charge_binding_id`,`in_month`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='收费账单表';

-- `eh_charge_binding`
CREATE TABLE `eh_charge_binding` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '绑定ID',
                                     `asset_type` int(4) NOT NULL COMMENT '资产类型:1房屋 2车位 3车辆 4住户仪表 5公共仪表 6其它资产',
                                     `asset_id` varchar(32) NOT NULL COMMENT '资产ID',
                                     `asset_name` varchar(200) NOT NULL COMMENT '资产名称',
                                     `charge_standard_id` bigint(20) NOT NULL COMMENT '收费标准ID',
                                     `period_num` int(11) DEFAULT '1' COMMENT '收费周期(每X个月作为一个周期进行收费)',
                                     `natural_period` int(11) DEFAULT '0' COMMENT '按照自然月生成账单:0否 1是',
                                     `community_id` varchar(32) NOT NULL COMMENT '小区ID',
                                     `is_active` int(11) DEFAULT '1' COMMENT '是否启用:0禁用 1启用',
                                     `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
                                     `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                     `update_time` varchar(19) DEFAULT '' COMMENT '更新时间',
                                     `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                     `start_time` int(11) DEFAULT '0' COMMENT '开始日期(yyyyMMdd)',
                                     `end_time` int(11) DEFAULT '0' COMMENT '结束日期(yyyyMMdd)',
                                     `next_bill_time` int(11) DEFAULT '0' COMMENT '下次账单日期(yyyyMMdd)',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_asset` (`asset_type`,`asset_id`),
                                     KEY `idx_standard` (`charge_standard_id`),
                                     KEY `idx_community` (`community_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COMMENT='收费绑定表';

-- `eh_charge_count_info`
CREATE TABLE `eh_charge_count_info` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '计算信息ID',
                                        `charge_standard_id` bigint(20) NOT NULL COMMENT '收费标准ID',
                                        `count_type` int(11) NOT NULL DEFAULT '100' COMMENT '计算类型',
                                        `area_type` int(11) DEFAULT '1' COMMENT '面积类型：1建筑面积 2套内面积 3使用面积',
                                        `area_name` varchar(50) DEFAULT '' COMMENT '面积名称',
                                        `price` decimal(10,2) DEFAULT '0.00' COMMENT '价格（分）',
                                        `unit` int(11) DEFAULT '1' COMMENT '单位',
                                        `ladder_type` int(11) DEFAULT '0' COMMENT '阶梯类型',
                                        `ladder_list` text COMMENT '阶梯列表JSON',
                                        `desc` varchar(500) DEFAULT '' COMMENT '描述',
                                        `regular_price` decimal(10,2) DEFAULT '0.00' COMMENT '常规价格',
                                        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `meter_price` decimal(10,2) DEFAULT '0.00' COMMENT '走表收费单价',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_charge_standard_id` (`charge_standard_id`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COMMENT='收费计算信息表';

-- `eh_charge_standard`
CREATE TABLE `eh_charge_standard` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收费标准ID',
                                      `fixed_amount` decimal(10,2) DEFAULT NULL COMMENT '固定金额',
                                      `is_active` int(11) NOT NULL DEFAULT '1' COMMENT '是否启用(1:启用,0:禁用)',
                                      `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                      `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
                                      `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
                                      `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                      `updated_by` varchar(30) DEFAULT '' COMMENT '更新人',
                                      `is_deleted` int(11) NOT NULL DEFAULT '0' COMMENT '是否删除(1:已删除,0:未删除)',
                                      `incomplete_month_handling` int(11) DEFAULT NULL,
                                      `charge_type` int(11) DEFAULT '1' COMMENT '收费类型：1周期性收费，2临时性收费',
                                      `round_type` int(11) DEFAULT '2' COMMENT '取整方式：0四舍五入，1抹零，2向上取整',
                                      `period_type` int(11) DEFAULT '101' COMMENT '收费周期类型',
                                      `count_type` int(11) DEFAULT '100' COMMENT '计算类型',
                                      `area_type` int(11) DEFAULT '1' COMMENT '面积类型：1建筑面积，2套内面积，3使用面积',
                                      `price` decimal(10,2) DEFAULT '0.00' COMMENT '价格（分为单位）',
                                      `accounting_period_type` int(11) DEFAULT '1' COMMENT '账期类型',
                                      `accounting_period_month` int(11) DEFAULT '0' COMMENT '账期月份',
                                      `late_money_type` int(11) DEFAULT '0' COMMENT '违约金类型',
                                      `late_money_time_type` int(11) DEFAULT '0' COMMENT '违约金时间类型',
                                      `late_money_after_day` int(11) DEFAULT '0' COMMENT '违约金生效天数',
                                      `late_money_proportion` decimal(5,2) DEFAULT '0.00' COMMENT '违约金比例',
                                      `late_money_desc` varchar(500) DEFAULT '' COMMENT '违约金描述',
                                      `category_id` int(11) DEFAULT '0' COMMENT '分类ID',
                                      `community_id` bigint(20) DEFAULT '0' COMMENT '小区ID',
                                      `name` varchar(100) DEFAULT '' COMMENT '收费名称（别名）',
                                      `unit` int(11) DEFAULT '1' COMMENT '单位类型',
                                      `accounting_period_day` int(11) DEFAULT '1' COMMENT '账期日期',
                                      `count_info` text COMMENT '计算信息JSON',
                                      `related_asset_count` int(11) DEFAULT '0' COMMENT '关联资产数量',
                                      `version` int(11) NOT NULL DEFAULT '1' COMMENT '版本号',
                                      `parent_id` bigint(20) DEFAULT NULL COMMENT '父级ID（原始记录ID）',
                                      `is_current` int(11) NOT NULL DEFAULT '1' COMMENT '是否当前版本(1:是,0:否)',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_is_active` (`is_active`),
                                      KEY `idx_created_at` (`created_at`),
                                      KEY `idx_parent_version` (`parent_id`,`version`),
                                      KEY `idx_current` (`is_current`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='收费标准表';

-- `eh_community`
CREATE TABLE `eh_community` (
                                `oc_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '小区唯一标识',
                                `oc_code` varchar(50) DEFAULT '' COMMENT '小区代码',
                                `oc_name` varchar(50) DEFAULT '' COMMENT '小区名称',
                                `oc_address` varchar(255) DEFAULT '' COMMENT '小区地址',
                                `oc_link` varchar(255) DEFAULT '' COMMENT '小区联系人信息',
                                `oc_state` int(11) DEFAULT '0' COMMENT '小区状态（0:正常, 1:维护中, 2:关闭）',
                                `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
                                `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
                                `update_time` varchar(19) DEFAULT '' COMMENT '修改时间',
                                `update_by` varchar(30) DEFAULT '' COMMENT '修改人',
                                `owner_count` int(11) DEFAULT '0' COMMENT '业主数量',
                                `building_num` int(11) DEFAULT '0' COMMENT '楼宇栋数',
                                `community_name` varchar(100) DEFAULT '' COMMENT '所属社区',
                                `oc_area` varchar(20) DEFAULT '' COMMENT '小区面积',
                                `pms_id` varchar(32) DEFAULT '' COMMENT '物业ID',
                                `pms_name` varchar(50) DEFAULT '' COMMENT '物业名称',
                                `manager_id` varchar(32) DEFAULT '' COMMENT '负责该小区的员工ID',
                                `service_phone` varchar(100) DEFAULT '' COMMENT '客服电话',
                                `ext_json` text COMMENT '小区核心配置',
                                `info_json` text COMMENT '小区扩展信息',
                                PRIMARY KEY (`oc_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10775 DEFAULT CHARSET=utf8mb4 COMMENT='小区信息表';

-- `eh_fee`
CREATE TABLE `eh_fee` (
                          `fee_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收费项目ID',
                          `name` varchar(100) NOT NULL COMMENT '收费项目名称',
                          `amount` decimal(10,2) NOT NULL COMMENT '收费金额',
                          `community_id` int(11) NOT NULL COMMENT '小区ID',
                          `due_date` date NOT NULL COMMENT '应缴日期',
                          PRIMARY KEY (`fee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='费用项目表';

-- `eh_file_download_log`
CREATE TABLE `eh_file_download_log` (
                                        `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
                                        `file_id` varchar(32) NOT NULL COMMENT '文件ID',
                                        `user_id` varchar(32) DEFAULT '' COMMENT '下载用户ID',
                                        `user_name` varchar(50) DEFAULT '' COMMENT '下载用户名',
                                        `user_type` varchar(20) DEFAULT 'wx_user' COMMENT '用户类型（wx_user/sys_user）',
                                        `download_ip` varchar(50) DEFAULT '' COMMENT '下载IP地址',
                                        `user_agent` varchar(500) DEFAULT '' COMMENT '用户代理',
                                        `download_time` varchar(19) NOT NULL COMMENT '下载时间',
                                        `status` char(1) DEFAULT '0' COMMENT '状态（0成功 1失败）',
                                        `error_msg` varchar(500) DEFAULT '' COMMENT '错误信息',
                                        `pms_id` varchar(32) DEFAULT '' COMMENT '关联物业ID',
                                        `community_id` varchar(50) DEFAULT '' COMMENT '关联小区ID',
                                        `owner_id` varchar(32) DEFAULT '' COMMENT '业主ID',
                                        `house_id` varchar(32) DEFAULT '' COMMENT '房屋ID',
                                        `house_name` varchar(200) DEFAULT '' COMMENT '房屋名称',
                                        PRIMARY KEY (`log_id`),
                                        KEY `idx_file_id` (`file_id`),
                                        KEY `idx_user_id` (`user_id`),
                                        KEY `idx_download_time` (`download_time`),
                                        KEY `idx_community_id` (`community_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='文件下载日志表';

-- `eh_file_folder`
CREATE TABLE `eh_file_folder` (
                                  `folder_id` varchar(32) NOT NULL COMMENT '文件夹ID',
                                  `parent_id` varchar(32) DEFAULT NULL COMMENT '父文件夹ID',
                                  `folder_name` varchar(100) NOT NULL COMMENT '文件夹名称',
                                  `folder_code` varchar(100) DEFAULT '' COMMENT '文件夹层级编码',
                                  `community_id` varchar(32) DEFAULT '0' COMMENT '所属小区ID，0表示公共文档库',
                                  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `create_user` varchar(64) DEFAULT '' COMMENT '创建用户',
                                  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1删除）',
                                  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                  PRIMARY KEY (`folder_id`),
                                  KEY `idx_parent_id` (`parent_id`),
                                  KEY `idx_community_id` (`community_id`),
                                  KEY `idx_status` (`status`),
                                  KEY `idx_folder_code` (`folder_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件夹信息表';

-- `eh_file_info`
CREATE TABLE `eh_file_info` (
                                `file_id` varchar(32) NOT NULL COMMENT '文件ID',
                                `community_id` varchar(32) DEFAULT '' COMMENT '所属小区',
                                `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
                                `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
                                `file_path` varchar(500) NOT NULL COMMENT '文件路径',
                                `access_url` varchar(500) NOT NULL COMMENT '访问URL',
                                `oss_url` varchar(500) DEFAULT NULL COMMENT 'OSS访问URL',
                                `oss_key` varchar(200) DEFAULT NULL COMMENT 'OSS存储键名',
                                `storage_type` varchar(20) DEFAULT 'local' COMMENT '存储类型：local-本地存储，oss-OSS存储，both-双重存储',
                                `file_size` bigint(20) DEFAULT '0' COMMENT '文件大小(字节)',
                                `file_size_str` varchar(20) DEFAULT '' COMMENT '格式化的文件大小',
                                `file_type` varchar(50) DEFAULT NULL COMMENT '文件扩展名',
                                `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
                                `upload_user` varchar(64) DEFAULT '' COMMENT '上传用户',
                                `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型(bx-报修,complaint-投诉建议)',
                                `business_id` varchar(32) DEFAULT NULL COMMENT '业务ID',
                                `folder_id` varchar(32) DEFAULT NULL COMMENT '所属文件夹ID',
                                `folder_code` varchar(100) DEFAULT '' COMMENT '文件夹层级编码',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1删除)',
                                `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                `absolute_path` varchar(1000) DEFAULT '' COMMENT '文件绝对路径',
                                `download_count` int(11) DEFAULT '0' COMMENT '下载次数',
                                `last_download_time` varchar(19) DEFAULT '' COMMENT '最后下载时间',
                                `source` varchar(30) DEFAULT '' COMMENT '业务来源',
                                PRIMARY KEY (`file_id`),
                                KEY `idx_create_time` (`create_time`) USING BTREE,
                                KEY `idx_status` (`status`) USING BTREE,
                                KEY `idx_file_type` (`file_type`) USING BTREE,
                                KEY `idx_business` (`business_type`,`business_id`) USING BTREE,
                                KEY `idx_folder_id` (`folder_id`),
                                KEY `idx_folder_code` (`folder_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件信息表';

-- `eh_house_info`
CREATE TABLE `eh_house_info` (
                                 `house_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '房屋ID',
                                 `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
                                 `community_name` varchar(50) DEFAULT NULL COMMENT '小区名称',
                                 `building_id` varchar(32) DEFAULT NULL COMMENT '楼栋ID',
                                 `building_name` varchar(20) DEFAULT NULL COMMENT '楼栋号',
                                 `unit_id` varchar(32) DEFAULT NULL COMMENT '单元ID',
                                 `unit_name` varchar(20) DEFAULT NULL COMMENT '单元名称',
                                 `combina_name` varchar(255) DEFAULT '' COMMENT '房屋全称',
                                 `floor` varchar(10) DEFAULT NULL COMMENT '楼层',
                                 `room` varchar(20) DEFAULT NULL COMMENT '房间号',
                                 `room_tag` varchar(30) DEFAULT '' COMMENT '房屋标签',
                                 `use_area` decimal(10,2) DEFAULT '0.00' COMMENT '使用面积',
                                 `total_area` decimal(10,2) DEFAULT '0.00' COMMENT '建筑面积',
                                 `house_type` varchar(10) DEFAULT '' COMMENT '房屋类型',
                                 `house_status` varchar(10) DEFAULT '' COMMENT '房屋状态',
                                 `check_status` int(11) DEFAULT '0' COMMENT '审核状态（0未审核 1已审核 2审核不通过）',
                                 `area` decimal(10,2) DEFAULT '0.00' COMMENT '房屋面积',
                                 `remark` varchar(500) DEFAULT '' COMMENT '备注',
                                 `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
                                 `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
                                 `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
                                 `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
                                 `owner_count` int(11) DEFAULT '0' COMMENT '绑定住户数量',
                                 `owner_str` varchar(255) DEFAULT '' COMMENT '关联住户',
                                 `arrear_amount` decimal(10,2) DEFAULT '0.00' COMMENT '欠费金额',
                                 `property_no` varchar(100) DEFAULT '',
                                 `house_tag` varchar(100) DEFAULT '',
                                 PRIMARY KEY (`house_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1999560 DEFAULT CHARSET=utf8mb4 COMMENT='房屋信息表';

-- `eh_house_invite`
CREATE TABLE `eh_house_invite` (
                                   `invite_id` varchar(32) NOT NULL COMMENT '邀请ID',
                                   `house_id` varchar(32) NOT NULL COMMENT '房屋ID',
                                   `inviter_id` varchar(32) NOT NULL COMMENT '邀请人ID',
                                   `invite_code` varchar(8) DEFAULT NULL COMMENT '邀请码',
                                   `invite_token` varchar(36) DEFAULT NULL COMMENT '邀请令牌（UUID）',
                                   `invite_phone` varchar(11) DEFAULT NULL COMMENT '被邀请人手机号',
                                   `qr_code_url` varchar(200) DEFAULT NULL COMMENT '二维码图片地址',
                                   `rel_type` int(11) DEFAULT '2' COMMENT '关系类型（1业主 2家庭成员 3租户）',
                                   `invite_type` tinyint(1) DEFAULT '1' COMMENT '邀请类型（1小程序卡片 2邀请码 3手机号）',
                                   `expire_time` datetime NOT NULL COMMENT '过期时间',
                                   `status` int(11) DEFAULT '0' COMMENT '状态（0待接受 1已接受 2已拒绝 3已过期）',
                                   `accept_user_id` varchar(32) DEFAULT NULL COMMENT '接受邀请的用户ID',
                                   `accept_time` datetime DEFAULT NULL COMMENT '接受时间',
                                   `used_time` datetime DEFAULT NULL COMMENT '使用时间',
                                   `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                   PRIMARY KEY (`invite_id`),
                                   UNIQUE KEY `uk_invite_code` (`invite_code`),
                                   UNIQUE KEY `uk_invite_token` (`invite_token`),
                                   KEY `idx_house_id` (`house_id`),
                                   KEY `idx_inviter_id` (`inviter_id`),
                                   KEY `idx_invite_phone` (`invite_phone`),
                                   KEY `idx_status` (`status`),
                                   KEY `idx_expire_time` (`expire_time`),
                                   KEY `idx_invite_type` (`invite_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋邀请记录表';

-- `eh_house_owner_rel`
CREATE TABLE `eh_house_owner_rel` (
                                      `rel_id` varchar(32) NOT NULL COMMENT '关系ID',
                                      `house_id` varchar(32) NOT NULL COMMENT '房屋ID',
                                      `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
                                      `community_id` varchar(32) DEFAULT NULL,
                                      `rel_type` int(11) DEFAULT '1' COMMENT '关系类型（1业主 2家庭成员 3租户）',
                                      `is_default` int(11) DEFAULT '0' COMMENT '是否默认（0否 1是）',
                                      `check_status` int(11) DEFAULT '1' COMMENT '审核状态（0未审核 1已审核 2审核不通过）',
                                      `apply_flag` int(11) DEFAULT '0' COMMENT '是否申请的数据 0 否 1是',
                                      `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                      `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
                                      `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
                                      `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
                                      `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
                                      `approve_info` json DEFAULT NULL COMMENT '审批信息JSON',
                                      `file_id` varchar(32) DEFAULT NULL,
                                      PRIMARY KEY (`rel_id`),
                                      KEY `idx_house_id` (`house_id`),
                                      KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋业主关系表';

-- `eh_markicam_config`
CREATE TABLE `eh_markicam_config` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                      `org_id` varchar(50) NOT NULL COMMENT 'Markicam组织ID',
                                      `api_key` varchar(255) NOT NULL COMMENT 'API密钥',
                                      `base_url` varchar(255) DEFAULT 'https://open-api.markiapp.com' COMMENT 'API基础URL',
                                      `is_enabled` int(11) DEFAULT '1' COMMENT '是否启用(0:禁用 1:启用)',
                                      `last_sync_time` varchar(19) DEFAULT '' COMMENT '最后同步时间',
                                      `sync_interval` int(11) DEFAULT '3600' COMMENT '同步间隔(秒)',
                                      `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
                                      `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
                                      `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
                                      `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
                                      `is_deleted` int(11) DEFAULT '0' COMMENT '是否删除(0:未删除 1:已删除)',
                                      `is_active` int(11) DEFAULT '1' COMMENT '是否激活(0:未激活 1:激活)',
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `uk_community_org` (`community_id`,`org_id`),
                                      KEY `idx_community_id` (`community_id`),
                                      KEY `idx_is_enabled` (`is_enabled`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='Markicam API配置表';

-- `eh_markicam_illegal_park`
CREATE TABLE `eh_markicam_illegal_park` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                            `team_id` int(11) NOT NULL COMMENT '团队ID',
                                            `report_uid` int(11) NOT NULL COMMENT '上报人用户ID',
                                            `car_plate` varchar(20) DEFAULT '' COMMENT '违规车辆车牌号',
                                            `car_picture` text COMMENT '违规车辆照片链接',
                                            `report_time` int(11) NOT NULL COMMENT '上报时间戳',
                                            `report_time_str` varchar(19) DEFAULT '' COMMENT '上报时间字符串',
                                            `sync_time` varchar(19) DEFAULT '' COMMENT '同步时间',
                                            `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
                                            `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
                                            `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
                                            `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
                                            `is_deleted` int(11) DEFAULT '0' COMMENT '是否删除(0:未删除 1:已删除)',
                                            `is_active` int(11) DEFAULT '1' COMMENT '是否激活(0:未激活 1:激活)',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_community_id` (`community_id`),
                                            KEY `idx_team_id` (`team_id`),
                                            KEY `idx_report_uid` (`report_uid`),
                                            KEY `idx_car_plate` (`car_plate`),
                                            KEY `idx_report_time` (`report_time`),
                                            KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Markicam违规车辆表';

-- `eh_markicam_member`
CREATE TABLE `eh_markicam_member` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                      `uid` int(11) NOT NULL COMMENT 'Markicam用户ID',
                                      `team_id` int(11) NOT NULL COMMENT '团队ID',
                                      `nickname` varchar(100) DEFAULT '' COMMENT '昵称',
                                      `phone` varchar(20) DEFAULT '' COMMENT '电话',
                                      `join_time` int(11) NOT NULL COMMENT '加入团队时间戳',
                                      `join_time_str` varchar(19) DEFAULT '' COMMENT '加入时间字符串',
                                      `member_type` int(11) NOT NULL COMMENT '成员类型(1:已注册主管理员 2:已注册管理员 3:已注册普通成员 4:未注册普通成员 5:未注册管理员)',
                                      `sync_time` varchar(19) DEFAULT '' COMMENT '同步时间',
                                      `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
                                      `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
                                      `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
                                      `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
                                      `is_deleted` int(11) DEFAULT '0' COMMENT '是否删除(0:未删除 1:已删除)',
                                      `is_active` int(11) DEFAULT '1' COMMENT '是否激活(0:未激活 1:激活)',
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `uk_community_uid_team` (`community_id`,`uid`,`team_id`),
                                      KEY `idx_community_id` (`community_id`),
                                      KEY `idx_team_id` (`team_id`),
                                      KEY `idx_uid` (`uid`),
                                      KEY `idx_member_type` (`member_type`),
                                      KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='Markicam团队成员表';

-- `eh_markicam_moment`
CREATE TABLE `eh_markicam_moment` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                      `markicam_id` varchar(50) NOT NULL COMMENT 'Markicam照片/视频ID',
                                      `uid` int(11) NOT NULL COMMENT '用户ID',
                                      `team_id` int(11) NOT NULL COMMENT '团队ID',
                                      `url` text COMMENT '照片/视频链接',
                                      `moment_type` int(11) NOT NULL COMMENT '动态类型(1:照片 2:视频)',
                                      `content` text COMMENT '水印内容',
                                      `mark_name` varchar(255) DEFAULT '' COMMENT '水印名称',
                                      `lng` decimal(10,6) DEFAULT NULL COMMENT '经度',
                                      `lat` decimal(10,6) DEFAULT NULL COMMENT '纬度',
                                      `post_time` int(11) NOT NULL COMMENT '上传时间戳',
                                      `post_time_str` varchar(19) DEFAULT '' COMMENT '上传时间字符串',
                                      `sync_time` varchar(19) DEFAULT '' COMMENT '同步时间',
                                      `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
                                      `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
                                      `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
                                      `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
                                      `is_deleted` int(11) DEFAULT '0' COMMENT '是否删除(0:未删除 1:已删除)',
                                      `is_active` int(11) DEFAULT '1' COMMENT '是否激活(0:未激活 1:激活)',
                                      `is_public` int(11) DEFAULT '1' COMMENT '是否公示(0:否 1:是)',
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `uk_markicam_id` (`markicam_id`),
                                      KEY `idx_community_id` (`community_id`),
                                      KEY `idx_team_id` (`team_id`),
                                      KEY `idx_uid` (`uid`),
                                      KEY `idx_moment_type` (`moment_type`),
                                      KEY `idx_post_time` (`post_time`),
                                      KEY `idx_sync_time` (`sync_time`),
                                      KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB AUTO_INCREMENT=3915 DEFAULT CHARSET=utf8mb4 COMMENT='Markicam照片视频表';

-- `eh_markicam_sync_log`
CREATE TABLE `eh_markicam_sync_log` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                        `sync_type` varchar(20) NOT NULL COMMENT '同步类型(moment/member/illegal_park/team)',
                                        `sync_status` int(11) NOT NULL COMMENT '同步状态(0:失败 1:成功 2:进行中)',
                                        `sync_count` int(11) DEFAULT '0' COMMENT '同步数量',
                                        `error_msg` text COMMENT '错误信息',
                                        `start_time` varchar(19) DEFAULT '' COMMENT '开始时间',
                                        `end_time` varchar(19) DEFAULT '' COMMENT '结束时间',
                                        `duration` int(11) DEFAULT '0' COMMENT '耗时(秒)',
                                        `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_community_id` (`community_id`),
                                        KEY `idx_sync_type` (`sync_type`),
                                        KEY `idx_sync_status` (`sync_status`),
                                        KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COMMENT='Markicam同步日志表';

-- `eh_markicam_team`
CREATE TABLE `eh_markicam_team` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                    `team_id` int(11) NOT NULL COMMENT 'Markicam团队ID',
                                    `team_name` varchar(100) DEFAULT '' COMMENT '团队名称',
                                    `team_desc` text COMMENT '团队描述',
                                    `member_count` int(11) DEFAULT '0' COMMENT '成员数量',
                                    `reg_member_count` int(11) DEFAULT '0' COMMENT '已注册成员数量',
                                    `unreg_member_count` int(11) DEFAULT '0' COMMENT '未注册成员数量',
                                    `sync_time` varchar(19) DEFAULT '' COMMENT '同步时间',
                                    `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
                                    `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
                                    `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
                                    `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
                                    `is_deleted` int(11) DEFAULT '0' COMMENT '是否删除(0:未删除 1:已删除)',
                                    `is_active` int(11) DEFAULT '1' COMMENT '是否激活(0:未激活 1:激活)',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `uk_community_team` (`community_id`,`team_id`),
                                    KEY `idx_community_id` (`community_id`),
                                    KEY `idx_team_id` (`team_id`),
                                    KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='Markicam团队信息表';

-- `eh_owner`
CREATE TABLE `eh_owner` (
                            `owner_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '业主ID',
                            `owner_name` varchar(50) DEFAULT '' COMMENT '业主姓名',
                            `pms_id` varchar(32) DEFAULT '' COMMENT '物业ID',
                            `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
                            `mobile` varchar(11) DEFAULT '' COMMENT '手机号码',
                            `id_card` varchar(18) DEFAULT '' COMMENT '身份证号码',
                            `gender` varchar(1) DEFAULT '' COMMENT '性别:M-男,F-女',
                            `address` varchar(200) DEFAULT '' COMMENT '家庭住址',
                            `face_photo` varchar(200) DEFAULT '' COMMENT '业主人脸照片地址',
                            `door_key_count` int(11) DEFAULT '0' COMMENT '门禁钥匙数量',
                            `contract` varchar(200) DEFAULT '' COMMENT '业主合同',
                            `is_default` int(11) NULL DEFAULT 0 COMMENT '是否默认（0否 1是）',
                            `house_id` varchar(32) DEFAULT NULL COMMENT '默认房屋ID',
                            `house_info` varchar(800) DEFAULT '' COMMENT '绑定房产',
                            `house_name` varchar(200) DEFAULT '' COMMENT '默认房屋信息',
                            `house_count` int(11) DEFAULT '0' COMMENT '房屋数量',
                            `member_count` int(11) DEFAULT '0' COMMENT '业主成员数',
                            `parking_count` int(11) DEFAULT '0' COMMENT '车位数量',
                            `parking_no` varchar(100) DEFAULT '' COMMENT '绑定车位号',
                            `car_count` int(11) DEFAULT '0' COMMENT '车辆数量',
                            `car_info` varchar(255) DEFAULT '' COMMENT '关联车牌',
                            `complaint_count` int(11) DEFAULT '0' COMMENT '投诉次数',
                            `repair_count` int(11) DEFAULT '0' COMMENT '报修次数',
                            `arrears_count` int(11) DEFAULT '0' COMMENT '欠费次数',
                            `remark` varchar(500) DEFAULT '' COMMENT '备注',
                            `move_date` varchar(10) DEFAULT '' COMMENT '居住日期',
                            `is_live` int(11) DEFAULT NULL COMMENT '入住状态',
                            `role` int(11) DEFAULT '1' COMMENT '角色 1业主，2业委会',
                            `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
                            `update_time` varchar(19) DEFAULT '' COMMENT '修改时间',
                            `creator` varchar(32) DEFAULT '' COMMENT '创建人',
                            `updater` varchar(32) DEFAULT '' COMMENT '修改人',
                            PRIMARY KEY (`owner_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1465507 DEFAULT CHARSET=utf8mb4 COMMENT='业主信息表';

-- `eh_parking_house_rel`
CREATE TABLE `eh_parking_house_rel` (
                                        `rel_id` varchar(32) NOT NULL COMMENT '关系ID',
                                        `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
                                        `house_id` varchar(32) NOT NULL COMMENT '房屋ID',
                                        `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
                                        `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认:0否 1是',
                                        `check_status` tinyint(1) DEFAULT '1' COMMENT '审核状态:0未审核 1已审核 2审核不通过',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`rel_id`),
                                        UNIQUE KEY `uk_parking_house` (`parking_id`,`house_id`),
                                        KEY `idx_house` (`house_id`),
                                        KEY `idx_parking` (`parking_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车位房屋关系表';

-- `eh_parking_owner_rel`
CREATE TABLE `eh_parking_owner_rel` (
                                        `rel_id` varchar(32) NOT NULL COMMENT '关系ID',
                                        `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
                                        `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
                                        `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
                                        `rel_type` int(11) DEFAULT '1' COMMENT '关系类型:1业主 2租户',
                                        `is_default` int(11) DEFAULT '0' COMMENT '是否默认:0否 1是',
                                        `check_status` int(11) DEFAULT '1' COMMENT '审核状态:0未审核 1已审核 2审核不通过',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`rel_id`),
                                        UNIQUE KEY `uk_parking_owner` (`parking_id`,`owner_id`),
                                        KEY `idx_owner` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车位业主关系表';

-- `eh_parking_space`
CREATE TABLE `eh_parking_space` (
                                    `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
                                    `community_id` varchar(32) NOT NULL COMMENT '小区ID',
                                    `parking_no` varchar(20) NOT NULL COMMENT '车位编号',
                                    `parking_type` int(11) NOT NULL COMMENT '车位类型:1私人车位 2子母车位',
                                    `parking_status` int(11) NOT NULL COMMENT '车位状态:1出售 2出租 3自用',
                                    `check_status` int(11) DEFAULT '0' COMMENT '审核状态:0未审核 1已审核 2审核不通过',
                                    `owner_count` int(11) DEFAULT '0' COMMENT '绑定业主数',
                                    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                    `create_by` varchar(30) DEFAULT '' COMMENT '创建者',
                                    `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(30) DEFAULT '' COMMENT '更新者',
                                    `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
                                    `parking_area` decimal(10,2) DEFAULT '0.00' COMMENT '车位面积(平方米)',
                                    `house_id` varchar(32) DEFAULT NULL COMMENT '绑定房屋ID',
                                    `house_name` varchar(100) DEFAULT '' COMMENT '房屋名称',
                                    `owner_name` varchar(100) DEFAULT '' COMMENT '业主名称',
                                    `plate_no` varchar(100) DEFAULT '' COMMENT '绑定车牌号',
                                    PRIMARY KEY (`parking_id`),
                                    KEY `idx_community` (`community_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车位信息表';

-- `eh_payment`
CREATE TABLE `eh_payment` (
                              `payment_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '缴费记录ID',
                              `owner_id` int(11) NOT NULL COMMENT '业主ID',
                              `fee_id` int(11) NOT NULL COMMENT '收费项目ID',
                              `amount` decimal(10,2) NOT NULL COMMENT '缴费金额',
                              `payment_date` date NOT NULL COMMENT '缴费日期',
                              `status` enum('已缴','未缴') NOT NULL COMMENT '缴费状态',
                              PRIMARY KEY (`payment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缴费记录表';

-- `eh_plate_recognition_log`
CREATE TABLE `eh_plate_recognition_log` (
                                            `log_id` varchar(32) NOT NULL COMMENT '记录ID',
                                            `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                            `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
                                            `user_name` varchar(100) DEFAULT NULL COMMENT '用户姓名',
                                            `user_phone` varchar(20) DEFAULT NULL COMMENT '用户手机号',
                                            `file_id` varchar(32) DEFAULT NULL COMMENT '上传文件ID',
                                            `plate_number` varchar(20) DEFAULT NULL COMMENT '识别出的车牌号',
                                            `recognition_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '识别状态(0:失败 1:成功)',
                                            `confidence` decimal(5,4) DEFAULT NULL COMMENT '识别置信度',
                                            `plate_color` varchar(20) DEFAULT NULL COMMENT '车牌颜色',
                                            `owner_found` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否找到车主(0:否 1:是)',
                                            `owner_id` varchar(32) DEFAULT NULL COMMENT '车主ID',
                                            `owner_name` varchar(100) DEFAULT NULL COMMENT '车主姓名',
                                            `owner_phone` varchar(20) DEFAULT NULL COMMENT '车主电话',
                                            `image_size` int(11) DEFAULT NULL COMMENT '图片大小(字节)',
                                            `recognition_time` int(11) DEFAULT NULL COMMENT '识别耗时(毫秒)',
                                            `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
                                            PRIMARY KEY (`log_id`),
                                            KEY `idx_community_id` (`community_id`),
                                            KEY `idx_user_id` (`user_id`),
                                            KEY `idx_file_id` (`file_id`),
                                            KEY `idx_plate_number` (`plate_number`),
                                            KEY `idx_create_time` (`create_time`),
                                            KEY `idx_recognition_status` (`recognition_status`),
                                            KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车牌识别记录表';

-- `eh_pms_bank_account`
CREATE TABLE `eh_pms_bank_account` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `pms_id` varchar(32) DEFAULT NULL COMMENT '物业公司ID',
                                       `community_id` varchar(32) DEFAULT NULL COMMENT '小区ID',
                                       `bank_type` varchar(32) DEFAULT 'CCB' COMMENT '银行类型（如CCB, ICBC等）',
                                       `cust_id` varchar(64) DEFAULT NULL COMMENT '客户号（CUST_ID）',
                                       `user_id` varchar(64) DEFAULT NULL COMMENT '用户号（USER_ID）',
                                       `password` varchar(128) DEFAULT NULL COMMENT '登录密码（建议加密存储）',
                                       `acc_no` varchar(64) DEFAULT NULL COMMENT '账号（ACC_NO）',
                                       `language` varchar(8) DEFAULT 'CN' COMMENT '语言',
                                       `api_url` varchar(128) DEFAULT NULL COMMENT '银行接口地址（如http://ip:port）',
                                       `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                       `status` tinyint(4) DEFAULT '0' COMMENT '状态（0=正常 1=停用）',
                                       `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                       `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                       `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
                                       `balance_last_time` varchar(19) DEFAULT '' COMMENT '最后取余额时间',
                                       `publish_status` varchar(20) DEFAULT 'draft' COMMENT '默认公示状态',
                                       `last_getdata_time` varchar(19) DEFAULT NULL COMMENT '最后同步账单时间',
                                       `last_getdata_date` int(10) DEFAULT NULL COMMENT '最后同步账单日期',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_pms_id` (`pms_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='物业银行账户配置表';

-- `eh_pms_info`
CREATE TABLE `eh_pms_info` (
                               `pms_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识符，主键',
                               `pms_name` varchar(255) DEFAULT '' COMMENT '物业名称',
                               `address` varchar(255) DEFAULT '' COMMENT '物业地址',
                               `manager` varchar(255) DEFAULT '' COMMENT '物业管理员',
                               `phone` varchar(20) DEFAULT '' COMMENT '联系电话',
                               `legal_person` varchar(255) DEFAULT '' COMMENT '公司法人',
                               `establishment_date` varchar(20) DEFAULT '' COMMENT '成立日期',
                               `landmark` varchar(255) DEFAULT '' COMMENT '地标',
                               `create_time` varchar(20) DEFAULT '' COMMENT '记录创建时间',
                               `status` int(11) DEFAULT '0' COMMENT '状态 0 正常 1暂停',
                               `employee_count` int(11) DEFAULT NULL COMMENT '员工数量',
                               `registration_number` varchar(50) DEFAULT '' COMMENT '注册号',
                               PRIMARY KEY (`pms_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10774 DEFAULT CHARSET=utf8mb4 COMMENT='物业信息表';

-- `eh_service_tel`
CREATE TABLE `eh_service_tel` (
                                  `service_tel_id` int(11) NOT NULL AUTO_INCREMENT,
                                  `parent_id` int(11) DEFAULT '0',
                                  `community_id` varchar(32) NOT NULL COMMENT '小区ID',
                                  `tel_number` varchar(20) NOT NULL COMMENT '电话号码',
                                  `tel_type` varchar(10) DEFAULT 'external' COMMENT '号码类型(internal=内部号码,external=外部号码)',
                                  `service_name` varchar(100) NOT NULL COMMENT '服务名称',
                                  `company_name` varchar(200) DEFAULT '' COMMENT '公司全称',
                                  `icon_url` varchar(500) DEFAULT '' COMMENT '图标地址',
                                  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
                                  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
                                  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
                                  `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
                                  `remark` varchar(255) DEFAULT '' COMMENT '备注',
                                  PRIMARY KEY (`service_tel_id`),
                                  KEY `idx_community_id` (`community_id`),
                                  KEY `idx_status` (`status`),
                                  KEY `idx_parent_id` (`parent_id`),
                                  KEY `idx_tel_type` (`tel_type`),
                                  KEY `idx_community_tel_type` (`community_id`,`tel_type`)
) ENGINE=InnoDB AUTO_INCREMENT=115 DEFAULT CHARSET=utf8mb4 COMMENT='服务电话信息表';

-- `eh_tran_record`
CREATE TABLE `eh_tran_record` (
                                  `trck_no` varchar(50) NOT NULL COMMENT '交易流水号',
                                  `pms_id` varchar(32) DEFAULT NULL COMMENT '所属物业',
                                  `community_id` varchar(32) DEFAULT NULL COMMENT '所属小区',
                                  `account_type_id` varchar(32) DEFAULT NULL COMMENT '所属账目分类',
                                  `account_type_name` varchar(50) DEFAULT '',
                                  `acct_no` varchar(50) DEFAULT NULL,
                                  `acct_name` varchar(30) DEFAULT NULL,
                                  `amt` decimal(10,2) DEFAULT NULL,
                                  `amt_all` decimal(10,2) DEFAULT NULL,
                                  `tran_date_id` int(10) DEFAULT NULL,
                                  `tran_date` varchar(10) DEFAULT NULL,
                                  `tran_time` varchar(10) DEFAULT NULL,
                                  `tran_datetime` varchar(19) DEFAULT '',
                                  `tran_month` varchar(10) DEFAULT '',
                                  `message` varchar(255) DEFAULT '',
                                  `real_tran_date` varchar(10) DEFAULT NULL,
                                  `status` varchar(20) DEFAULT 'draft',
                                  `direction` varchar(10) DEFAULT 'in',
                                  `remark` varchar(255) DEFAULT '',
                                  `bank_no` varchar(50) DEFAULT NULL,
                                  `cust_bank_name` varchar(50) DEFAULT NULL,
                                  `tran_flow_no` varchar(50) DEFAULT NULL,
                                  PRIMARY KEY (`trck_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- `eh_unit`
CREATE TABLE `eh_unit` (
                           `unit_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '单元ID',
                           `building_id` varchar(32) NOT NULL COMMENT '楼栋ID',
                           `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
                           `name` varchar(50) NOT NULL COMMENT '单元名称或编号',
                           `house_count` int(11) DEFAULT '0' COMMENT '户数',
                           `house_area` decimal(10,2) DEFAULT '0.00' COMMENT '房屋面积',
                           `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                           `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
                           `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
                           `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
                           `remark` varchar(255) DEFAULT '' COMMENT '备注',
                           PRIMARY KEY (`unit_id`),
                           KEY `idx_building_id` (`building_id`)
) ENGINE=InnoDB AUTO_INCREMENT=86598 DEFAULT CHARSET=utf8mb4 COMMENT='单元信息表';

-- `eh_vehicle`
CREATE TABLE `eh_vehicle` (
                              `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
                              `community_id` varchar(32) NOT NULL COMMENT '小区ID',
                              `plate_no` varchar(20) NOT NULL COMMENT '车牌号',
                              `vehicle_type` varchar(20) NOT NULL DEFAULT '' COMMENT '车辆类型(1:业主车辆 2:公共车辆)',
                              `vehicle_brand` varchar(50) DEFAULT NULL COMMENT '车辆品牌',
                              `vehicle_model` varchar(50) DEFAULT NULL COMMENT '车辆型号',
                              `owner_count` int(11) DEFAULT '0' COMMENT '绑定业主数',
                              `check_status` int(11) DEFAULT '1' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
                              `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                              `create_by` varchar(30) DEFAULT '' COMMENT '创建者',
                              `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
                              `update_by` varchar(30) DEFAULT '' COMMENT '更新者',
                              `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
                              `plate_type` varchar(50) DEFAULT NULL COMMENT '车牌类型',
                              `owner_id` varchar(32) DEFAULT NULL COMMENT '住户ID',
                              `owner_name` varchar(100) DEFAULT '' COMMENT '住户姓名',
                              `parking_space` varchar(50) DEFAULT '' COMMENT '车位信息',
                              `is_owner_ticket` int(11) DEFAULT '0' COMMENT '是否为业主车辆票',
                              `parking_space_id` varchar(32) DEFAULT NULL COMMENT '车位ID',
                              `house_id` varchar(32) DEFAULT NULL COMMENT '房屋ID',
                              `house_name` varchar(100) DEFAULT '' COMMENT '房屋名称',
                              `owner_real_name` varchar(50) DEFAULT '' COMMENT '车主姓名',
                              `owner_phone` varchar(20) DEFAULT '' COMMENT '车主号码',
                              PRIMARY KEY (`vehicle_id`),
                              KEY `idx_plate_no` (`plate_no`),
                              KEY `idx_community_plate` (`community_id`,`plate_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆信息表';

-- `eh_vehicle_house_rel`
CREATE TABLE `eh_vehicle_house_rel` (
                                        `rel_id` varchar(32) NOT NULL COMMENT '关联ID',
                                        `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
                                        `house_id` varchar(32) NOT NULL COMMENT '房屋ID',
                                        `is_default` int(11) DEFAULT '0' COMMENT '是否默认(0:否 1:是)',
                                        `check_status` int(11) DEFAULT '1' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`rel_id`),
                                        KEY `idx_vehicle_id` (`vehicle_id`),
                                        KEY `idx_owner_id` (`house_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆房屋关联表';

-- `eh_vehicle_owner_rel`
CREATE TABLE `eh_vehicle_owner_rel` (
                                        `rel_id` varchar(32) NOT NULL COMMENT '关联ID',
                                        `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
                                        `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
                                        `is_default` int(11) DEFAULT '0' COMMENT '是否默认(0:否 1:是)',
                                        `check_status` int(11) DEFAULT '1' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`rel_id`),
                                        KEY `idx_vehicle_id` (`vehicle_id`),
                                        KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆业主关联表';

-- `eh_vehicle_parking_rel`
CREATE TABLE `eh_vehicle_parking_rel` (
                                          `rel_id` varchar(32) NOT NULL COMMENT '关联ID',
                                          `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
                                          `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
                                          `is_default` int(11) DEFAULT '0' COMMENT '是否默认(0:否 1:是)',
                                          `check_status` int(11) DEFAULT '1' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
                                          `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                          `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`rel_id`),
                                          KEY `idx_vehicle_id` (`vehicle_id`),
                                          KEY `idx_parking_id` (`parking_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆车位关联表';

-- `eh_wx_bx`
CREATE TABLE `eh_wx_bx` (
                            `id` varchar(32) NOT NULL COMMENT '主键',
                            `community_id` varchar(32) DEFAULT NULL,
                            `wx_user_id` varchar(32) DEFAULT NULL COMMENT '微信用户ID',
                            `owner_id` varchar(32) DEFAULT NULL COMMENT '业主ID',
                            `type` varchar(20) DEFAULT NULL COMMENT '报修类型',
                            `content` text COMMENT '反馈内容',
                            `media_urls` text COMMENT '图片/视频URL，JSON数组',
                            `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
                            `name` varchar(50) DEFAULT NULL COMMENT '姓名',
                            `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
                            `status` int(11) DEFAULT '0',
                            `create_time` datetime DEFAULT NULL,
                            `update_time` datetime DEFAULT NULL,
                            `create_by` varchar(50) DEFAULT NULL,
                            `update_by` varchar(50) DEFAULT NULL,
                            `handler` varchar(255) DEFAULT NULL COMMENT '处理人',
                            `feedback` text COMMENT '处理反馈',
                            `handling_time` datetime DEFAULT NULL COMMENT '处理时间',
                            `ys_status` int(11) DEFAULT '0' COMMENT '验收状态：0-待验收，1-验收通过，2-验收不通过',
                            `ys_reply` text COMMENT '验收回复内容',
                            `ys_time` datetime DEFAULT NULL COMMENT '验收时间',
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信报修表';

-- `eh_wx_complaint`
CREATE TABLE `eh_wx_complaint` (
                                   `id` varchar(32) NOT NULL COMMENT '主键',
                                   `community_id` varchar(32) DEFAULT NULL,
                                   `wx_user_id` varchar(32) DEFAULT NULL COMMENT '微信用户ID',
                                   `owner_id` varchar(32) DEFAULT NULL COMMENT '业主ID',
                                   `type` varchar(20) DEFAULT NULL COMMENT '类型(投诉 or 建议)',
                                   `content` text COMMENT '反馈内容',
                                   `media_urls` text COMMENT '图片/视频URL，JSON数组',
                                   `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
                                   `name` varchar(50) DEFAULT NULL COMMENT '姓名',
                                   `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
                                   `status` int(11) DEFAULT '0',
                                   `create_time` varchar(19) DEFAULT NULL,
                                   `update_time` varchar(19) DEFAULT NULL,
                                   `create_by` varchar(50) DEFAULT NULL,
                                   `update_by` varchar(50) DEFAULT NULL,
                                   `handler` varchar(255) DEFAULT NULL COMMENT '处理人',
                                   `feedback` text COMMENT '处理反馈',
                                   `handling_time` datetime DEFAULT NULL COMMENT '处理时间',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信投诉建议表';

-- `eh_wx_config`
CREATE TABLE `eh_wx_config` (
                                `conf_id` varchar(255) NOT NULL,
                                `conf_key` varchar(100) DEFAULT NULL,
                                `conf_value` text,
                                `community_id` varchar(32) DEFAULT NULL,
                                PRIMARY KEY (`conf_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- `eh_wx_message_log`
CREATE TABLE `eh_wx_message_log` (
                                     `id` varchar(32) NOT NULL COMMENT '主键ID',
                                     `wx_user_id` varchar(32) NOT NULL COMMENT '微信用户ID',
                                     `owner_id` varchar(32) DEFAULT NULL COMMENT '业主ID',
                                     `openid` varchar(64) NOT NULL COMMENT '用户openid',
                                     `template_id` varchar(64) NOT NULL COMMENT '消息模板ID',
                                     `template_type` varchar(20) NOT NULL COMMENT '模板类型',
                                     `business_id` varchar(32) DEFAULT NULL COMMENT '业务ID(报修ID、通知ID等)',
                                     `business_type` varchar(20) DEFAULT NULL COMMENT '业务类型(repair:报修, notice:通知)',
                                     `message_data` text COMMENT '消息内容JSON',
                                     `send_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发送状态(0:待发送, 1:发送成功, 2:发送失败)',
                                     `send_time` datetime DEFAULT NULL COMMENT '发送时间',
                                     `error_msg` text COMMENT '错误信息',
                                     `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
                                     `community_id` varchar(32) DEFAULT NULL COMMENT '社区ID',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_wx_user` (`wx_user_id`),
                                     KEY `idx_openid` (`openid`),
                                     KEY `idx_business` (`business_id`,`business_type`),
                                     KEY `idx_send_status` (`send_status`),
                                     KEY `idx_template_type` (`template_type`),
                                     KEY `idx_community` (`community_id`),
                                     KEY `idx_owner` (`owner_id`),
                                     KEY `idx_community_business` (`community_id`,`business_type`,`send_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信消息发送日志表';

-- `eh_wx_nav`
CREATE TABLE `eh_wx_nav` (
                             `nav_id` int(11) NOT NULL AUTO_INCREMENT,
                             `parent_id` int(11) DEFAULT '0' COMMENT '父菜单ID(0表示顶级菜单)',
                             `community_id` varchar(32) DEFAULT NULL,
                             `nav_name` varchar(50) DEFAULT '',
                             `content` longtext,
                             `pdf_file` varchar(500) DEFAULT '',
                             `pdf_file_id` varchar(32) DEFAULT NULL,
                             `sort` int(11) DEFAULT NULL,
                             `icon_name` varchar(30) DEFAULT '',
                             `tap_name` varchar(50) DEFAULT NULL,
                             `status` int(11) DEFAULT '0',
                             `is_default` int(11) DEFAULT '0',
                             `create_time` varchar(19) DEFAULT NULL,
                             `update_time` varchar(19) DEFAULT NULL,
                             `update_by` varchar(30) DEFAULT NULL,
                             `nav_type` varchar(30) DEFAULT 'text',
                             `nav_code` varchar(30) DEFAULT NULL,
                             `source` varchar(50) DEFAULT NULL COMMENT '新增的来源字段',
                             `pdf_file_list` text COMMENT '新增的PDF文件列表',
                             `url` varchar(500) DEFAULT '' COMMENT 'URL链接地址，用于nav_type为url时',
                             `html_content` longtext COMMENT 'HTML代码内容，用于nav_type为html时',
                             `miniprogram_config` varchar(200) DEFAULT '' COMMENT '小程序配置参数(JSON格式)',
                             `top_show` int(11) DEFAULT '0' COMMENT '置顶显示',
                             `remark` varchar(255) DEFAULT '',
                             PRIMARY KEY (`nav_id`),
                             KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=129 DEFAULT CHARSET=utf8mb4;

-- `eh_wx_share_record`
CREATE TABLE `eh_wx_share_record` (
                                      `id` varchar(32) NOT NULL COMMENT '主键ID',
                                      `user_id` varchar(32) DEFAULT NULL COMMENT '分享用户ID',
                                      `openid` varchar(64) NOT NULL COMMENT '分享用户openid',
                                      `nickname` varchar(50) DEFAULT NULL COMMENT '分享用户昵称',
                                      `share_type` varchar(20) NOT NULL COMMENT '分享类型(app_message:分享给朋友, timeline:分享到朋友圈)',
                                      `share_source` varchar(50) NOT NULL DEFAULT 'index' COMMENT '分享来源页面(index:首页, notice:公告, repair:报修等)',
                                      `source_id` varchar(32) DEFAULT NULL COMMENT '来源内容ID(如公告ID、报修ID等)',
                                      `source_title` varchar(200) DEFAULT NULL COMMENT '来源内容标题',
                                      `share_title` varchar(200) DEFAULT NULL COMMENT '分享标题',
                                      `share_desc` varchar(500) DEFAULT NULL COMMENT '分享描述',
                                      `share_path` varchar(500) DEFAULT NULL COMMENT '分享路径',
                                      `share_image` varchar(500) DEFAULT NULL COMMENT '分享图片',
                                      `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                      `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
                                      `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_user_id` (`user_id`),
                                      KEY `idx_openid` (`openid`),
                                      KEY `idx_community_id` (`community_id`),
                                      KEY `idx_share_source` (`share_source`),
                                      KEY `idx_source_id` (`source_id`),
                                      KEY `idx_create_time` (`create_time`),
                                      KEY `idx_share_record_community_time` (`community_id`,`create_time`),
                                      KEY `idx_share_record_source_time` (`share_source`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信分享记录表';

-- `eh_wx_share_statistics`
CREATE TABLE `eh_wx_share_statistics` (
                                          `id` varchar(36) NOT NULL COMMENT '主键ID',
                                          `share_record_id` varchar(32) NOT NULL COMMENT '分享记录ID',
                                          `total_visits` int(11) DEFAULT '0' COMMENT '总访问次数',
                                          `unique_visitors` int(11) DEFAULT '0' COMMENT '独立访客数',
                                          `new_users` int(11) DEFAULT '0' COMMENT '新用户数',
                                          `total_stay_duration` int(11) DEFAULT '0' COMMENT '总停留时长(秒)',
                                          `avg_stay_duration` decimal(10,2) DEFAULT '0.00' COMMENT '平均停留时长(秒)',
                                          `last_visit_time` datetime DEFAULT NULL COMMENT '最后访问时间',
                                          `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          PRIMARY KEY (`id`),
                                          UNIQUE KEY `uk_share_record_id` (`share_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信分享统计汇总表';

-- `eh_wx_share_visit`
CREATE TABLE `eh_wx_share_visit` (
                                     `id` varchar(32) NOT NULL COMMENT '主键ID',
                                     `share_record_id` varchar(32) NOT NULL COMMENT '分享记录ID',
                                     `visitor_openid` varchar(64) DEFAULT NULL COMMENT '访问者openid(未登录时为空)',
                                     `visitor_user_id` varchar(32) DEFAULT NULL COMMENT '访问者用户ID(未登录时为空)',
                                     `visitor_nickname` varchar(50) DEFAULT NULL COMMENT '访问者昵称',
                                     `is_new_user` tinyint(1) DEFAULT '0' COMMENT '是否新用户(0:否, 1:是)',
                                     `visit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
                                     `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
                                     `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
                                     `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
                                     `stay_duration` int(11) DEFAULT '0' COMMENT '停留时长(秒)',
                                     `page_views` int(11) DEFAULT '1' COMMENT '页面浏览数',
                                     `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_share_record_id` (`share_record_id`),
                                     KEY `idx_visitor_openid` (`visitor_openid`),
                                     KEY `idx_visitor_user_id` (`visitor_user_id`),
                                     KEY `idx_community_id` (`community_id`),
                                     KEY `idx_visit_time` (`visit_time`),
                                     KEY `idx_is_new_user` (`is_new_user`),
                                     KEY `idx_share_visit_time_community` (`visit_time`,`community_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信分享访问记录表';

-- `eh_wx_subscribe_record`
CREATE TABLE `eh_wx_subscribe_record` (
                                          `id` varchar(32) NOT NULL COMMENT '主键ID',
                                          `wx_user_id` varchar(32) NOT NULL COMMENT '微信用户ID',
                                          `owner_id` varchar(32) DEFAULT NULL COMMENT '业主ID',
                                          `openid` varchar(64) NOT NULL COMMENT '用户openid',
                                          `template_id` varchar(64) NOT NULL COMMENT '消息模板ID',
                                          `template_type` varchar(20) NOT NULL COMMENT '模板类型(repair_notice:报修提醒, property_notice:物业通知)',
                                          `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '订阅状态(0:未订阅, 1:已订阅)',
                                          `subscribe_time` datetime DEFAULT NULL COMMENT '订阅时间',
                                          `expire_time` datetime DEFAULT NULL COMMENT '过期时间(一次性订阅)',
                                          `community_id` varchar(32) NOT NULL COMMENT '社区ID',
                                          `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          PRIMARY KEY (`id`),
                                          UNIQUE KEY `uk_user_template` (`wx_user_id`,`template_id`),
                                          KEY `idx_openid` (`openid`),
                                          KEY `idx_template_type` (`template_type`),
                                          KEY `idx_community` (`community_id`),
                                          KEY `idx_owner` (`owner_id`),
                                          KEY `idx_status` (`status`),
                                          KEY `idx_expire_time` (`expire_time`),
                                          KEY `idx_community_template` (`community_id`,`template_type`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信订阅消息记录表';

-- `eh_wx_user`
CREATE TABLE `eh_wx_user` (
                              `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                              `openid` varchar(64) NOT NULL COMMENT '微信openid',
                              `owner_id` varchar(32) DEFAULT '',
                              `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
                              `session_key` varchar(64) DEFAULT NULL COMMENT '会话密钥',
                              `nick_name` varchar(50) DEFAULT NULL COMMENT '用户昵称',
                              `avatar_url` varchar(255) DEFAULT NULL COMMENT '用户头像',
                              `mobile` varchar(11) DEFAULT NULL COMMENT '手机号码',
                              `gender` char(1) DEFAULT '0' COMMENT '用户性别（0未知 1男 2女）',
                              `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
                              `login_ip` varchar(128) DEFAULT NULL COMMENT '最后登录IP',
                              `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                              `community_id` varchar(32) DEFAULT NULL COMMENT '所属小区ID',
                              `user_roles` varchar(50) DEFAULT NULL COMMENT '用户角色：owner=业主,property=物业，多个用逗号分隔',
                              PRIMARY KEY (`user_id`),
                              UNIQUE KEY `idx_openid` (`openid`),
                              KEY `idx_unionid` (`unionid`)
) ENGINE=InnoDB AUTO_INCREMENT=150 DEFAULT CHARSET=utf8mb4 COMMENT='微信用户表';

-- `gen_table`
CREATE TABLE `gen_table` (
                             `table_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                             `table_name` varchar(200) DEFAULT '' COMMENT '表名称',
                             `table_comment` varchar(500) DEFAULT '' COMMENT '表描述',
                             `sub_table_name` varchar(64) DEFAULT NULL COMMENT '关联子表的表名',
                             `sub_table_fk_name` varchar(64) DEFAULT NULL COMMENT '子表关联的外键名',
                             `class_name` varchar(100) DEFAULT '' COMMENT '实体类名称',
                             `tpl_category` varchar(200) DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作 sub主子表操作）',
                             `package_name` varchar(100) DEFAULT NULL COMMENT '生成包路径',
                             `module_name` varchar(30) DEFAULT NULL COMMENT '生成模块名',
                             `business_name` varchar(30) DEFAULT NULL COMMENT '生成业务名',
                             `function_name` varchar(50) DEFAULT NULL COMMENT '生成功能名',
                             `function_author` varchar(50) DEFAULT NULL COMMENT '生成功能作者',
                             `form_col_num` int(1) DEFAULT '1' COMMENT '表单布局（单列 双列 三列）',
                             `gen_type` char(1) DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
                             `gen_path` varchar(200) DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
                             `options` varchar(1000) DEFAULT NULL COMMENT '其它生成选项',
                             `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                             `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                             `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                             `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                             PRIMARY KEY (`table_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='代码生成业务表';

-- `gen_table_column`
CREATE TABLE `gen_table_column` (
                                    `column_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                    `table_id` bigint(20) DEFAULT NULL COMMENT '归属表编号',
                                    `column_name` varchar(200) DEFAULT NULL COMMENT '列名称',
                                    `column_comment` varchar(500) DEFAULT NULL COMMENT '列描述',
                                    `column_type` varchar(100) DEFAULT NULL COMMENT '列类型',
                                    `java_type` varchar(500) DEFAULT NULL COMMENT 'JAVA类型',
                                    `java_field` varchar(200) DEFAULT NULL COMMENT 'JAVA字段名',
                                    `is_pk` char(1) DEFAULT NULL COMMENT '是否主键（1是）',
                                    `is_increment` char(1) DEFAULT NULL COMMENT '是否自增（1是）',
                                    `is_required` char(1) DEFAULT NULL COMMENT '是否必填（1是）',
                                    `is_insert` char(1) DEFAULT NULL COMMENT '是否为插入字段（1是）',
                                    `is_edit` char(1) DEFAULT NULL COMMENT '是否编辑字段（1是）',
                                    `is_list` char(1) DEFAULT NULL COMMENT '是否列表字段（1是）',
                                    `is_query` char(1) DEFAULT NULL COMMENT '是否查询字段（1是）',
                                    `query_type` varchar(200) DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
                                    `html_type` varchar(200) DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
                                    `dict_type` varchar(200) DEFAULT '' COMMENT '字典类型',
                                    `sort` int(11) DEFAULT NULL COMMENT '排序',
                                    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`column_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COMMENT='代码生成业务表字段';

-- `sys_comment_like`
CREATE TABLE `sys_comment_like` (
                                    `like_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
                                    `comment_id` bigint(20) NOT NULL COMMENT '评论ID',
                                    `user_id` varchar(32) NOT NULL COMMENT '用户ID',
                                    `user_name` varchar(50) NOT NULL COMMENT '用户名',
                                    `user_type` varchar(20) NOT NULL DEFAULT 'wx_user' COMMENT '用户类型（wx_user/sys_user）',
                                    `status` char(1) DEFAULT '0' COMMENT '状态（0有效 1已取消）',
                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `owner_id` varchar(32) DEFAULT '',
                                    `house_id` varchar(32) DEFAULT '',
                                    `house_name` varchar(200) DEFAULT '',
                                    PRIMARY KEY (`like_id`),
                                    UNIQUE KEY `uk_comment_user` (`comment_id`,`user_id`),
                                    KEY `idx_comment_id` (`comment_id`),
                                    KEY `idx_user_id` (`user_id`),
                                    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞记录表';

-- `sys_config`
CREATE TABLE `sys_config` (
                              `config_id` int(5) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
                              `config_name` varchar(100) DEFAULT '' COMMENT '参数名称',
                              `config_key` varchar(100) DEFAULT '' COMMENT '参数键名',
                              `config_value` varchar(500) DEFAULT '' COMMENT '参数键值',
                              `config_type` char(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
                              `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                              `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                              PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='参数配置表';

-- `sys_dept`
CREATE TABLE `sys_dept` (
                            `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
                            `parent_id` bigint(20) DEFAULT '0' COMMENT '父部门id',
                            `pms_id` varchar(32) DEFAULT '' COMMENT '关联物业公司id',
                            `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
                            `dept_name` varchar(30) DEFAULT '' COMMENT '部门名称',
                            `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
                            `leader` varchar(20) DEFAULT NULL COMMENT '负责人',
                            `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
                            `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
                            `status` char(1) DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
                            `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                            `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=209 DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- `sys_dict_data`
CREATE TABLE `sys_dict_data` (
                                 `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
                                 `dict_sort` int(4) DEFAULT '0' COMMENT '字典排序',
                                 `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
                                 `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
                                 `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
                                 `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
                                 `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
                                 `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
                                 `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                 `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                 PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=127 DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';

-- `sys_dict_type`
CREATE TABLE `sys_dict_type` (
                                 `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
                                 `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
                                 `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
                                 `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                 `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                 PRIMARY KEY (`dict_id`),
                                 UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=105 DEFAULT CHARSET=utf8mb4 COMMENT='字典类型表';

-- `sys_job`
CREATE TABLE `sys_job` (
                           `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
                           `job_name` varchar(64) NOT NULL DEFAULT '' COMMENT '任务名称',
                           `job_group` varchar(64) NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
                           `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
                           `cron_expression` varchar(255) DEFAULT '' COMMENT 'cron执行表达式',
                           `misfire_policy` varchar(20) DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
                           `concurrent` char(1) DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
                           `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1暂停）',
                           `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                           `remark` varchar(500) DEFAULT '' COMMENT '备注信息',
                           PRIMARY KEY (`job_id`,`job_name`,`job_group`)
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb4 COMMENT='定时任务调度表';

-- `sys_job_log`
CREATE TABLE `sys_job_log` (
                               `job_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
                               `job_name` varchar(64) NOT NULL COMMENT '任务名称',
                               `job_group` varchar(64) NOT NULL COMMENT '任务组名',
                               `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
                               `job_message` varchar(500) DEFAULT NULL COMMENT '日志信息',
                               `status` char(1) DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
                               `exception_info` varchar(2000) DEFAULT '' COMMENT '异常信息',
                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                               PRIMARY KEY (`job_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=952 DEFAULT CHARSET=utf8mb4 COMMENT='定时任务调度日志表';

-- `sys_logininfor`
CREATE TABLE `sys_logininfor` (
                                  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
                                  `login_name` varchar(50) DEFAULT '' COMMENT '登录账号',
                                  `ipaddr` varchar(128) DEFAULT '' COMMENT '登录IP地址',
                                  `login_location` varchar(255) DEFAULT '' COMMENT '登录地点',
                                  `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
                                  `os` varchar(50) DEFAULT '' COMMENT '操作系统',
                                  `status` char(1) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
                                  `msg` varchar(255) DEFAULT '' COMMENT '提示消息',
                                  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
                                  PRIMARY KEY (`info_id`),
                                  KEY `idx_sys_logininfor_s` (`status`),
                                  KEY `idx_sys_logininfor_lt` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=950 DEFAULT CHARSET=utf8mb4 COMMENT='系统访问记录';

-- `sys_menu`
CREATE TABLE `sys_menu` (
                            `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
                            `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
                            `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
                            `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
                            `url` varchar(200) DEFAULT '#' COMMENT '请求地址',
                            `target` varchar(20) DEFAULT '' COMMENT '打开方式（menuItem页签 menuBlank新窗口）',
                            `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
                            `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
                            `is_refresh` char(1) DEFAULT '1' COMMENT '是否刷新（0刷新 1不刷新）',
                            `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
                            `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
                            `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) DEFAULT '' COMMENT '备注',
                            PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2048 DEFAULT CHARSET=utf8mb4 COMMENT='菜单权限表';

-- `sys_menu_click_log`
CREATE TABLE `sys_menu_click_log` (
                                      `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
                                      `menu_id` varchar(32) NOT NULL COMMENT '菜单ID',
                                      `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
                                      `menu_type` varchar(20) DEFAULT '' COMMENT '菜单类型(text/pdf/url/page/miniprogram)',
                                      `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID（系统用户）',
                                      `user_name` varchar(50) DEFAULT '' COMMENT '用户名称',
                                      `first_click_time` datetime DEFAULT NULL COMMENT '首次点击时间',
                                      `last_click_time` datetime NOT NULL COMMENT '最后点击时间',
                                      `click_date` date NOT NULL COMMENT '点击日期（用于每天只记录一次）',
                                      `click_count` int(11) DEFAULT '1' COMMENT '当天点击次数',
                                      `ip_address` varchar(128) DEFAULT '' COMMENT 'IP地址',
                                      `owner_id` varchar(32) DEFAULT '' COMMENT '业主ID',
                                      `house_id` varchar(32) DEFAULT '' COMMENT '房屋ID',
                                      `house_name` varchar(200) DEFAULT '' COMMENT '房屋名称',
                                      `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
                                      `source` varchar(20) DEFAULT '' COMMENT '来源页面(index/nav/mine)',
                                      PRIMARY KEY (`log_id`) USING BTREE,
                                      UNIQUE KEY `uk_menu_user_date` (`menu_id`,`user_id`,`click_date`) USING BTREE,
                                      KEY `idx_menu_id` (`menu_id`) USING BTREE,
                                      KEY `idx_user_id` (`user_id`) USING BTREE,
                                      KEY `idx_click_date` (`click_date`) USING BTREE,
                                      KEY `idx_community_id` (`community_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=225 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='菜单点击日志表';

-- `sys_notice`
CREATE TABLE `sys_notice` (
                              `notice_id` int(4) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
                              `pms_id` varchar(32) DEFAULT '' COMMENT '关联物业ID',
                              `community_id` varchar(50) DEFAULT '' COMMENT '关联小区ID',
                              `notice_title` varchar(50) NOT NULL COMMENT '公告标题',
                              `notice_type` varchar(20) NOT NULL DEFAULT '' COMMENT '公告类型（1通知 2公告）',
                              `notice_content` longblob COMMENT '公告内容',
                              `status` char(1) DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
                              `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                              `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                              `read_count` int(11) DEFAULT '0' COMMENT '阅读次数',
                              `comment_count` int(11) DEFAULT '0' COMMENT '评论次数',
                              `is_top` int(11) DEFAULT '0' COMMENT '是否置顶（0否 1是）',
                              `like_count` int(11) DEFAULT '0' COMMENT '点赞次数',
                              `share_count` int(11) DEFAULT '0' COMMENT '分享次数',
                              `publisher_type` varchar(20) DEFAULT 'property' COMMENT '发布者类型：property-物业，committee-业委会',
                              `signature` varchar(100) DEFAULT '' COMMENT '署名',
                              `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除（0未删除 1已删除）',
                              `enable_comment` int(11) DEFAULT '0' COMMENT '是否开启评论（0关闭 1开启）',
                              `house_read_count` int(11) DEFAULT '0' COMMENT '房屋阅读数（去重统计）',
                              PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='通知公告表';

-- `sys_notice_comment`
CREATE TABLE `sys_notice_comment` (
                                      `comment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
                                      `notice_id` int(4) NOT NULL COMMENT '公告ID',
                                      `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID（回复功能）',
                                      `user_id` varchar(32) NOT NULL COMMENT '评论用户ID',
                                      `user_name` varchar(50) NOT NULL COMMENT '评论用户名',
                                      `user_type` varchar(20) NOT NULL DEFAULT 'wx_user' COMMENT '用户类型（wx_user/sys_user）',
                                      `content` text NOT NULL COMMENT '评论内容',
                                      `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1删除 2审核中）',
                                      `pms_id` varchar(32) DEFAULT '' COMMENT '关联物业ID',
                                      `community_id` varchar(50) DEFAULT '' COMMENT '关联小区ID',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                      `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                      `like_count` int(11) DEFAULT '0' COMMENT '点赞次数',
                                      `owner_id` varchar(32) DEFAULT '',
                                      `house_id` varchar(32) DEFAULT '',
                                      `house_name` varchar(200) DEFAULT '',
                                      PRIMARY KEY (`comment_id`),
                                      KEY `idx_notice_id` (`notice_id`),
                                      KEY `idx_parent_id` (`parent_id`),
                                      KEY `idx_user_id` (`user_id`),
                                      KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知公告评论表';

-- `sys_notice_like`
CREATE TABLE `sys_notice_like` (
                                   `like_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
                                   `notice_id` int(4) NOT NULL COMMENT '公告ID',
                                   `user_id` varchar(32) NOT NULL COMMENT '用户ID',
                                   `user_name` varchar(50) NOT NULL COMMENT '用户名',
                                   `user_type` varchar(20) NOT NULL DEFAULT 'wx_user' COMMENT '用户类型（wx_user/sys_user）',
                                   `community_id` varchar(50) DEFAULT '' COMMENT '关联小区ID',
                                   `status` char(1) DEFAULT '0' COMMENT '状态（0有效 1已取消）',
                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `owner_id` varchar(32) DEFAULT '',
                                   `house_id` varchar(32) DEFAULT '',
                                   `house_name` varchar(200) DEFAULT '',
                                   PRIMARY KEY (`like_id`),
                                   UNIQUE KEY `uk_notice_user` (`notice_id`,`user_id`),
                                   KEY `idx_notice_id` (`notice_id`),
                                   KEY `idx_user_id` (`user_id`),
                                   KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告点赞记录表';

-- `sys_notice_read_log`
CREATE TABLE `sys_notice_read_log` (
                                       `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
                                       `notice_id` int(4) NOT NULL COMMENT '公告ID',
                                       `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID（系统用户）',
                                       `user_name` varchar(50) DEFAULT '' COMMENT '用户名称',
                                       `read_time` datetime NOT NULL COMMENT '阅读时间',
                                       `ip_address` varchar(128) DEFAULT '' COMMENT 'IP地址',
                                       `owner_id` varchar(32) DEFAULT '',
                                       `house_id` varchar(32) DEFAULT '',
                                       `house_name` varchar(200) DEFAULT '',
                                       PRIMARY KEY (`log_id`),
                                       KEY `idx_notice_id` (`notice_id`),
                                       KEY `idx_user_id` (`user_id`),
                                       KEY `idx_read_time` (`read_time`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COMMENT='通知公告阅读记录表';

-- `sys_notice_share`
CREATE TABLE `sys_notice_share` (
                                    `share_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分享ID',
                                    `notice_id` int(4) NOT NULL COMMENT '公告ID',
                                    `user_id` varchar(32) NOT NULL COMMENT '用户ID',
                                    `user_name` varchar(50) NOT NULL COMMENT '用户名',
                                    `user_type` varchar(20) NOT NULL DEFAULT 'wx_user' COMMENT '用户类型（wx_user/sys_user）',
                                    `community_id` varchar(50) DEFAULT '' COMMENT '关联小区ID',
                                    `share_platform` varchar(20) DEFAULT 'wechat' COMMENT '分享平台（wechat/timeline/other）',
                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                    `owner_id` varchar(32) DEFAULT '',
                                    `house_id` varchar(32) DEFAULT '',
                                    `house_name` varchar(200) DEFAULT '',
                                    `visit_count` int(11) DEFAULT '0' COMMENT '访问次数',
                                    `last_visit_time` datetime DEFAULT NULL COMMENT '最后访问时间',
                                    `last_visitor_id` varchar(32) DEFAULT NULL COMMENT '最后访问者ID',
                                    `last_visitor_name` varchar(50) DEFAULT NULL COMMENT '最后访问者姓名',
                                    PRIMARY KEY (`share_id`),
                                    KEY `idx_notice_id` (`notice_id`),
                                    KEY `idx_user_id` (`user_id`),
                                    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='公告分享记录表';

-- `sys_notice_share_visit`
CREATE TABLE `sys_notice_share_visit` (
                                          `visit_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问记录ID',
                                          `share_id` bigint(20) NOT NULL COMMENT '分享记录ID',
                                          `notice_id` int(4) NOT NULL COMMENT '公告ID',
                                          `visitor_id` varchar(32) DEFAULT NULL COMMENT '访问者用户ID',
                                          `visitor_name` varchar(50) DEFAULT NULL COMMENT '访问者姓名',
                                          `visitor_type` varchar(20) DEFAULT 'wx_user' COMMENT '访问者类型',
                                          `visit_time` datetime NOT NULL COMMENT '访问时间',
                                          `visit_ip` varchar(50) DEFAULT NULL COMMENT '访问IP',
                                          `user_agent` text COMMENT '用户代理',
                                          `community_id` varchar(50) DEFAULT NULL COMMENT '社区ID',
                                          `owner_id` varchar(32) DEFAULT NULL COMMENT '业主ID',
                                          `house_id` varchar(32) DEFAULT NULL COMMENT '房屋ID',
                                          `house_name` varchar(200) DEFAULT NULL COMMENT '房屋名称',
                                          `is_new_visitor` tinyint(1) DEFAULT '0' COMMENT '是否新访问者',
                                          `stay_duration` int(11) DEFAULT '0' COMMENT '停留时长(秒)',
                                          PRIMARY KEY (`visit_id`),
                                          KEY `idx_share_id` (`share_id`),
                                          KEY `idx_notice_id` (`notice_id`),
                                          KEY `idx_visitor_id` (`visitor_id`),
                                          KEY `idx_visit_time` (`visit_time`),
                                          KEY `idx_community_id` (`community_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告分享访问记录表';

-- `sys_oper_log`
CREATE TABLE `sys_oper_log` (
                                `oper_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
                                `title` varchar(50) DEFAULT '' COMMENT '模块标题',
                                `business_type` int(2) DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
                                `method` varchar(200) DEFAULT '' COMMENT '方法名称',
                                `request_method` varchar(10) DEFAULT '' COMMENT '请求方式',
                                `operator_type` int(1) DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
                                `oper_name` varchar(50) DEFAULT '' COMMENT '操作人员',
                                `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
                                `oper_url` varchar(255) DEFAULT '' COMMENT '请求URL',
                                `oper_ip` varchar(128) DEFAULT '' COMMENT '主机地址',
                                `oper_location` varchar(255) DEFAULT '' COMMENT '操作地点',
                                `oper_param` varchar(2000) DEFAULT '' COMMENT '请求参数',
                                `json_result` varchar(2000) DEFAULT '' COMMENT '返回参数',
                                `status` int(1) DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
                                `error_msg` varchar(2000) DEFAULT '' COMMENT '错误消息',
                                `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
                                `cost_time` bigint(20) DEFAULT '0' COMMENT '消耗时间',
                                PRIMARY KEY (`oper_id`),
                                KEY `idx_sys_oper_log_bt` (`business_type`),
                                KEY `idx_sys_oper_log_s` (`status`),
                                KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1475 DEFAULT CHARSET=utf8mb4 COMMENT='操作日志记录';

-- `sys_post`
CREATE TABLE `sys_post` (
                            `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
                            `post_code` varchar(64) NOT NULL COMMENT '岗位编码',
                            `post_name` varchar(50) NOT NULL COMMENT '岗位名称',
                            `post_sort` int(4) NOT NULL COMMENT '显示顺序',
                            `status` char(1) NOT NULL COMMENT '状态（0正常 1停用）',
                            `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='岗位信息表';

-- `sys_role`
CREATE TABLE `sys_role` (
                            `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
                            `role_name` varchar(30) NOT NULL COMMENT '角色名称',
                            `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
                            `role_sort` int(4) NOT NULL COMMENT '显示顺序',
                            `data_scope` char(1) DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
                            `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
                            `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                            `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COMMENT='角色信息表';

-- `sys_role_dept`
CREATE TABLE `sys_role_dept` (
                                 `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                 `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
                                 PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和部门关联表';

-- `sys_role_menu`
CREATE TABLE `sys_role_menu` (
                                 `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                 `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
                                 PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和菜单关联表';

-- `sys_user`
CREATE TABLE `sys_user` (
                            `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                            `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
                            `community_id` varchar(50) DEFAULT NULL COMMENT '当前选择的社区ID',
                            `login_name` varchar(30) NOT NULL COMMENT '登录账号',
                            `user_name` varchar(30) DEFAULT '' COMMENT '用户昵称',
                            `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户 01注册用户）',
                            `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
                            `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
                            `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
                            `avatar` varchar(100) DEFAULT '' COMMENT '头像路径',
                            `password` varchar(50) DEFAULT '' COMMENT '密码',
                            `salt` varchar(20) DEFAULT '' COMMENT '盐加密',
                            `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
                            `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                            `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
                            `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
                            `pwd_update_date` datetime DEFAULT NULL COMMENT '密码最后更新时间',
                            `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- `sys_user_online`
CREATE TABLE `sys_user_online` (
                                   `sessionId` varchar(50) NOT NULL DEFAULT '' COMMENT '用户会话id',
                                   `login_name` varchar(50) DEFAULT '' COMMENT '登录账号',
                                   `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
                                   `ipaddr` varchar(128) DEFAULT '' COMMENT '登录IP地址',
                                   `login_location` varchar(255) DEFAULT '' COMMENT '登录地点',
                                   `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
                                   `os` varchar(50) DEFAULT '' COMMENT '操作系统',
                                   `status` varchar(10) DEFAULT '' COMMENT '在线状态on_line在线off_line离线',
                                   `start_timestamp` datetime DEFAULT NULL COMMENT 'session创建时间',
                                   `last_access_time` datetime DEFAULT NULL COMMENT 'session最后访问时间',
                                   `expire_time` int(5) DEFAULT '0' COMMENT '超时时间，单位为分钟',
                                   PRIMARY KEY (`sessionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='在线用户记录';

-- `sys_user_post`
CREATE TABLE `sys_user_post` (
                                 `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                 `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
                                 PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户与岗位关联表';

-- `sys_user_role`
CREATE TABLE `sys_user_role` (
                                 `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                 `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                 PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户和角色关联表';

-- `wx_login_log`
CREATE TABLE `wx_login_log` (
                                `log_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
                                `user_id` varchar(32) NOT NULL COMMENT '用户ID',
                                `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
                                `login_ip` varchar(50) DEFAULT NULL COMMENT '登录IP',
                                `login_date` datetime DEFAULT NULL COMMENT '登录时间',
                                `user_agent` text COMMENT '用户代理信息',
                                PRIMARY KEY (`log_id`),
                                KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引'
) ENGINE=InnoDB AUTO_INCREMENT=497 DEFAULT CHARSET=utf8mb4 COMMENT='微信登录日志表';

