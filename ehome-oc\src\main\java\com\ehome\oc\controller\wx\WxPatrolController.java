package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 小程序巡更控制器
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/api/wx/patrol")
public class WxPatrolController extends BaseWxController {

    /**
     * 获取今日巡更任务列表
     */
    @PostMapping("/getTodayTasks")
    public AjaxResult getTodayTasks() {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }
            
            String userId = getCurrentUser().getUserId().toString();
            String communityId = getCurrentUser().getCommunityId();
            String today = DateUtils.dateTimeNow("yyyy-MM-dd");
            
            // 获取当前用户今日的巡更任务
            List<Record> tasks = Db.find(
                "SELECT r.*, c.longitude as target_lng, c.latitude as target_lat, c.location_range, c.project_name " +
                "FROM eh_patrol_record r " +
                "LEFT JOIN eh_patrol_config c ON r.config_id = c.config_id " +
                "WHERE r.patrol_user_id = ? AND r.community_id = ? AND r.patrol_date = ? " +
                "ORDER BY r.planned_time, r.create_time",
                userId, communityId, today);
            
            // 统计任务状态
            int totalTasks = tasks.size();
            int completedTasks = 0;
            int pendingTasks = 0;
            
            for (Record task : tasks) {
                int status = task.getInt("status");
                if (status == 1) {
                    completedTasks++;
                } else {
                    pendingTasks++;
                }
            }
            
            JSONObject result = new JSONObject();
            result.put("tasks", recordToMap(tasks));
            result.put("totalTasks", totalTasks);
            result.put("completedTasks", completedTasks);
            result.put("pendingTasks", pendingTasks);
            result.put("completionRate", totalTasks > 0 ? 
                new BigDecimal(completedTasks * 100.0 / totalTasks).setScale(1, RoundingMode.HALF_UP) : 0);
            
            return AjaxResult.success(result);
            
        } catch (Exception e) {
            logger.error("获取今日巡更任务失败", e);
            return AjaxResult.error("获取任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取巡更任务详情
     */
    @GetMapping("/getTaskDetail/{recordId}")
    public AjaxResult getTaskDetail(@PathVariable("recordId") String recordId) {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }
            
            String userId = getCurrentUser().getUserId().toString();
            String communityId = getCurrentUser().getCommunityId();
            
            Record task = Db.findFirst(
                "SELECT r.*, c.longitude as target_lng, c.latitude as target_lat, c.location_range, c.project_name " +
                "FROM eh_patrol_record r " +
                "LEFT JOIN eh_patrol_config c ON r.config_id = c.config_id " +
                "WHERE r.record_id = ? AND r.patrol_user_id = ? AND r.community_id = ?",
                recordId, userId, communityId);
            
            if (task == null) {
                return AjaxResult.error("任务不存在或无权限访问");
            }
            
            return AjaxResult.success(task.toMap());
            
        } catch (Exception e) {
            logger.error("获取巡更任务详情失败", e);
            return AjaxResult.error("获取任务详情失败：" + e.getMessage());
        }
    }

    /**
     * 提交巡更记录
     */
    @PostMapping("/submit")
    public AjaxResult submitPatrolRecord() {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }
            
            JSONObject params = getParams();
            String userId = getCurrentUser().getUserId().toString();
            String communityId = getCurrentUser().getCommunityId();
            String currentTime = DateUtils.getTime();
            
            String recordId = params.getString("recordId");
            if (StringUtils.isEmpty(recordId)) {
                return AjaxResult.error("记录ID不能为空");
            }
            
            // 验证任务是否存在且属于当前用户
            Record task = Db.findFirst(
                "SELECT * FROM eh_patrol_record WHERE record_id = ? AND patrol_user_id = ? AND community_id = ?",
                recordId, userId, communityId);
            
            if (task == null) {
                return AjaxResult.error("任务不存在或无权限操作");
            }
            
            if (task.getInt("status") == 1) {
                return AjaxResult.error("该任务已完成，无法重复提交");
            }
            
            // 验证必填字段
            JSONArray photoFileIds = params.getJSONArray("photoFileIds");
            if (photoFileIds == null || photoFileIds.size() == 0) {
                return AjaxResult.error("请至少上传一张照片");
            }
            
            Double longitude = params.getDouble("longitude");
            Double latitude = params.getDouble("latitude");
            if (longitude == null || latitude == null || longitude == 0 || latitude == 0) {
                return AjaxResult.error("请获取当前位置信息");
            }
            
            // 验证位置是否在允许范围内
            Record config = Db.findFirst(
                "SELECT * FROM eh_patrol_config WHERE config_id = ?", task.getStr("config_id"));
            
            if (config != null && config.getBigDecimal("longitude") != null && config.getBigDecimal("latitude") != null) {
                double targetLng = config.getBigDecimal("longitude").doubleValue();
                double targetLat = config.getBigDecimal("latitude").doubleValue();
                double distance = calculateDistance(latitude, longitude, targetLat, targetLng);
                double allowedRange = config.getInt("location_range");
                
                if (distance > allowedRange) {
                    return AjaxResult.error(String.format("当前位置距离目标地点%.0f米，超出允许范围%.0f米", distance, allowedRange));
                }
            }
            
            // 更新巡更记录
            String updateSql = "UPDATE eh_patrol_record SET " +
                "actual_time = ?, photo_file_ids = ?, photo_count = ?, " +
                "longitude = ?, latitude = ?, location_accuracy = ?, " +
                "distance_from_target = ?, remark = ?, status = 1, update_time = ? " +
                "WHERE record_id = ?";
            
            double distance = 0;
            if (config != null && config.getBigDecimal("longitude") != null) {
                double targetLng = config.getBigDecimal("longitude").doubleValue();
                double targetLat = config.getBigDecimal("latitude").doubleValue();
                distance = calculateDistance(latitude, longitude, targetLat, targetLng);
            }
            
            Db.update(updateSql,
                currentTime,
                photoFileIds.toString(),
                photoFileIds.size(),
                longitude,
                latitude,
                params.getDouble("accuracy") != null ? params.getDouble("accuracy") : 0,
                distance,
                params.getString("remark") != null ? params.getString("remark") : "",
                currentTime,
                recordId);
            
            return AjaxResult.success("巡更记录提交成功");
            
        } catch (Exception e) {
            logger.error("提交巡更记录失败", e);
            return AjaxResult.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 获取历史巡更记录
     */
    @PostMapping("/getRecords")
    public AjaxResult getPatrolRecords() {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }
            
            JSONObject params = getParams();
            String userId = getCurrentUser().getUserId().toString();
            String communityId = getCurrentUser().getCommunityId();
            
            StringBuilder sql = new StringBuilder();
            String baseFileds = "SELECT r.*, c.longitude as target_lng, c.latitude as target_lat, c.project_name ";
            sql.append(baseFileds);
            sql.append("FROM eh_patrol_record r ");
            sql.append("LEFT JOIN eh_patrol_config c ON r.config_id = c.config_id ");
            sql.append("WHERE r.patrol_user_id = '").append(userId).append("' ");
            sql.append("AND r.community_id = '").append(communityId).append("' ");
            
            // 添加日期筛选
            if (StringUtils.isNotEmpty(params.getString("startDate"))) {
                sql.append("AND r.patrol_date >= '").append(params.getString("startDate")).append("' ");
            }
            if (StringUtils.isNotEmpty(params.getString("endDate"))) {
                sql.append("AND r.patrol_date <= '").append(params.getString("endDate")).append("' ");
            }

            // 添加状态筛选
            if (StringUtils.isNotEmpty(params.getString("status"))) {
                sql.append("AND r.status = ").append(params.getString("status")).append(" ");
            }

            sql.append("ORDER BY r.patrol_date DESC, r.planned_time DESC");

            // 分页处理
            int pageNum = params.getInteger("pageNum") != null ? params.getInteger("pageNum") : 1;
            int pageSize = params.getInteger("pageSize") != null ? params.getInteger("pageSize") : 10;
            int offset = (pageNum - 1) * pageSize;
            
            String countSql = sql.toString().replace(baseFileds, "SELECT COUNT(*) ");
            int total = Db.queryInt(countSql);
            
            sql.append(" LIMIT ").append(offset).append(", ").append(pageSize);
            List<Record> records = Db.find(sql.toString());
            
            JSONObject result = new JSONObject();
            result.put("records", recordToMap(records));
            result.put("total", total);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", (total + pageSize - 1) / pageSize);
            
            return AjaxResult.success(result);
            
        } catch (Exception e) {
            logger.error("获取巡更记录失败", e);
            return AjaxResult.error("获取记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取本月统计数据
     */
    @PostMapping("/getMonthlyStats")
    public AjaxResult getMonthlyStats() {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = getCurrentUser().getUserId().toString();
            String communityId = getCurrentUser().getCommunityId();
            String currentMonth = DateUtils.dateTimeNow("yyyy-MM");

            // 获取本月已巡查的天数
            String patrolDaysSql = "SELECT COUNT(DISTINCT patrol_date) as patrol_days " +
                "FROM eh_patrol_record " +
                "WHERE patrol_user_id = ? AND community_id = ? " +
                "AND DATE_FORMAT(patrol_date, '%Y-%m') = ? AND status = 1";

            Record patrolDaysResult = Db.findFirst(patrolDaysSql, userId, communityId, currentMonth);
            int patrolDays = patrolDaysResult != null ? patrolDaysResult.getInt("patrol_days") : 0;

            // 获取本月异常次数（这里定义异常为：超时完成或距离目标点过远的记录）
            String abnormalCountSql = "SELECT COUNT(*) as abnormal_count " +
                "FROM eh_patrol_record r " +
                "LEFT JOIN eh_patrol_config c ON r.config_id = c.config_id " +
                "WHERE r.patrol_user_id = ? AND r.community_id = ? " +
                "AND DATE_FORMAT(r.patrol_date, '%Y-%m') = ? AND r.status = 1 " +
                "AND (r.distance_from_target > c.location_range OR " +
                "TIME(r.actual_time) > TIME(r.planned_time) + INTERVAL 30 MINUTE)";

            Record abnormalResult = Db.findFirst(abnormalCountSql, userId, communityId, currentMonth);
            int abnormalCount = abnormalResult != null ? abnormalResult.getInt("abnormal_count") : 0;

            JSONObject result = new JSONObject();
            result.put("patrolDays", patrolDays);
            result.put("abnormalCount", abnormalCount);
            result.put("currentMonth", currentMonth);

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("获取本月统计数据失败", e);
            return AjaxResult.error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取往日任务按日期分组
     */
    @PostMapping("/getHistoryByDate")
    public AjaxResult getHistoryByDate() {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = getCurrentUser().getUserId().toString();
            String communityId = getCurrentUser().getCommunityId();
            String today = DateUtils.dateTimeNow("yyyy-MM-dd");

            // 获取最近10天的巡更记录（不包括今天）
            String sql = "SELECT patrol_date, " +
                "COUNT(*) as total_tasks, " +
                "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as completed_tasks, " +
                "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_tasks " +
                "FROM eh_patrol_record " +
                "WHERE patrol_user_id = ? AND community_id = ? " +
                "AND patrol_date < ? AND patrol_date >= DATE_SUB(?, INTERVAL 10 DAY) " +
                "GROUP BY patrol_date " +
                "ORDER BY patrol_date DESC";

            List<Record> historyData = Db.find(sql, userId, communityId, today, today);

            JSONArray result = new JSONArray();
            for (Record record : historyData) {
                JSONObject dayData = new JSONObject();
                dayData.put("date", record.getStr("patrol_date"));
                dayData.put("totalTasks", record.getInt("total_tasks"));
                dayData.put("completedTasks", record.getInt("completed_tasks"));
                dayData.put("pendingTasks", record.getInt("pending_tasks"));
                result.add(dayData);
            }

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("获取往日任务数据失败", e);
            return AjaxResult.error("获取历史数据失败：" + e.getMessage());
        }
    }

    /**
     * 计算两点间距离（米）
     * 使用Haversine公式
     */
    private double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final double R = 6371000; // 地球半径（米）

        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLngRad = Math.toRadians(lng2 - lng1);

        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }
}
