package com.ehome.oc.domain;

import com.ehome.common.annotation.Excel;
import com.ehome.common.core.domain.BaseEntity;

import java.math.BigDecimal;

public class HouseInfo extends BaseEntity {
    private Long houseId;
    
    /** 用户ID */
    private Long wxUserId;

    /** 小区ID */
    private String communityId;

    private String ownerId;

    /** 业主 */
    private String ownerStr;
    
    /** 小区名称 */
    private String communityName;

    /** 楼栋ID */
    private String buildingId;

    /** 楼栋号 */
    @Excel(name = "楼栋名称", type = Excel.Type.IMPORT)
    private String building;

    /** 单元ID */
    private String unitId;

    /** 单元号 */
    @Excel(name = "单元名称", type = Excel.Type.IMPORT)
    private String unit;

    /** 房间号 */
    @Excel(name = "房间号", type = Excel.Type.IMPORT)
    private String room;

    private String combinaName;

    /** 楼层 */
    @Excel(name = "楼层", type = Excel.Type.IMPORT)
    private String floor;

    /** 使用面积 */
    @Excel(name = "使用面积", type = Excel.Type.IMPORT)
    private BigDecimal useArea;

    /** 建筑面积 */
    @Excel(name = "建筑面积", type = Excel.Type.IMPORT)
    private BigDecimal totalArea;

    /** 房屋类型 */
    @Excel(name = "房屋类型", type = Excel.Type.IMPORT, readConverterExp = "1=住宅,2=公寓,3=商铺,4=办公")
    private String houseType;

    private OwnerInfo ownerInfo;

    /** 房屋面积 */
    private BigDecimal area;
    
    /** 业主姓名 */
    private String ownerName;
    
    /** 联系电话 */
    private String ownerPhone;
    
    /** 身份证号 */
    private String idCard;

    /** 房屋状态 */
    @Excel(name = "房屋状态", type = Excel.Type.IMPORT, readConverterExp = "2001=已入住,2002=未销售,2003=已交房,2004=未入住,2005=已装修,2009=装修中")
    private String houseStatus;

    private int checkStatus;

    /** 审核状态（0未审核 1已审核 2审核不通过） */
    private Integer status;

    /** 是否默认（0否 1是） */
    private Integer isDefault;

    /** 审核备注 */
    @Excel(name = "备注", type = Excel.Type.IMPORT)
    private String remark;

    public Long getHouseId() {
        return houseId;
    }

    public void setHouseId(Long houseId) {
        this.houseId = houseId;
    }

    public Long getWxUserId() {
        return wxUserId;
    }

    public void setWxUserId(Long wxUserId) {
        this.wxUserId = wxUserId;
    }

    public String getOwnerStr() {
        return ownerStr;
    }

    public void setOwnerStr(String ownerStr) {
        this.ownerStr = ownerStr;
    }

    public String getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(String buildingId) {
        this.buildingId = buildingId;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getBuilding() {
        return building;
    }

    public void setBuilding(String building) {
        this.building = building;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public BigDecimal getArea() {
        return area;
    }

    public void setArea(BigDecimal area) {
        this.area = area;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerPhone() {
        return ownerPhone;
    }

    public void setOwnerPhone(String ownerPhone) {
        this.ownerPhone = ownerPhone;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public Integer getStatus() {
        return status;
    }


    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }


    public String getCombinaName() {
        return combinaName;
    }

    public void setCombinaName(String combinaName) {
        this.combinaName = combinaName;
    }

    public String getHouseStatus() {
        return houseStatus;
    }

    public void setHouseStatus(String houseStatus) {
        this.houseStatus = houseStatus;
    }

    public int getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(int checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public BigDecimal getUseArea() {
        return useArea;
    }

    public void setUseArea(BigDecimal useArea) {
        this.useArea = useArea;
    }

    public BigDecimal getTotalArea() {
        return totalArea;
    }

    public void setTotalArea(BigDecimal totalArea) {
        this.totalArea = totalArea;
    }

    public String getHouseType() {
        return houseType;
    }

    public void setHouseType(String houseType) {
        this.houseType = houseType;
    }

    public OwnerInfo getOwnerInfo() {
        return ownerInfo;
    }
    public void setOwnerInfo(OwnerInfo ownerInfo) {
        this.ownerInfo = ownerInfo;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }
}