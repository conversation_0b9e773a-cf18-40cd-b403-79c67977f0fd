// app.js
import 'umtrack-wx';
import { getStateManager } from './utils/stateManager.js'
import { mixinWatermark } from './mixins/watermarkMixin.js'
import { getUpdateManager } from './utils/updateManager.js'

// 全局页面拦截器
const originalPage = Page
Page = function(options) {
  // 不需要登录检查的页面白名单
  const noAuthPages = [
    'pages/login/index',
    'pages/demo/index',
    'pages/about/index',
    'pages/about/privacy',
    'pages/about/agreement',
    'pages/invite/accept'
  ]

  const originalOnLoad = options.onLoad || function() {}
  const originalOnShow = options.onShow || function() {}

  // 重写 onLoad，在页面加载时检查是否需要登录验证
  options.onLoad = function(query) {
    // 获取当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage ? currentPage.route : ''

    // 如果不在白名单中，进行登录检查
    if (!noAuthPages.includes(currentRoute)) {
      if (!this.checkLoginStatus()) return
    }

    originalOnLoad.call(this, query)
  }

  // 重写 onShow，在页面显示时检查登录状态
  options.onShow = function() {
    // 获取当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage ? currentPage.route : ''

    // 如果不在白名单中，进行登录检查
    if (!noAuthPages.includes(currentRoute)) {
      if (!this.checkLoginStatus()) return
    }

    originalOnShow.call(this)
  }

  // 为所有页面添加通用方法
  options.checkLoginStatus = function() {
    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()

      // 检查登录状态
      if (!state.isLogin) {
        // 获取当前页面路径
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        const currentRoute = currentPage ? currentPage.route : ''

        // 如果是首页且未登录，跳转到demo页面
        if (currentRoute === 'pages/index/index') {
          wx.navigateTo({
            url: '/pages/demo/index'
          })
        } else {
          // 其他页面跳转到登录页
          wx.navigateTo({
            url: '/pages/login/index'
          })
        }
        return false
      }
      return true
    } catch (error) {
      console.error('[LoginCheck] 登录检查失败:', error)
      // 如果状态管理器出错，获取当前页面路径决定跳转目标
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage ? currentPage.route : ''

      if (currentRoute === 'pages/index/index') {
        wx.navigateTo({
          url: '/pages/demo/index'
        })
      } else {
        wx.navigateTo({
          url: '/pages/login/index'
        })
      }
      return false
    }
  }

  options.checkLoginAndAuth = function() {
    if (!this.checkLoginStatus()) return false

    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()

      if (!state.isHouseAuth) {
        wx.showModal({
          title: '提示',
          content: '请先完成房屋认证',
          showCancel: false,
          confirmText: '去认证',
          success: () => {
            wx.switchTab({ url: '/pages/house/index' })
          }
        })
        return false
      }
      return true
    } catch (error) {
      console.error('[AuthCheck] 认证检查失败:', error)
      return false
    }
  }

  options.refreshPageState = function() {
    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()

      this.setData({
        isLogin: state.isLogin || false,
        hasBindPhone: state.hasBindPhone || false,
        isHouseAuth: state.isHouseAuth || false,
        userInfo: state.userInfo || null,
        ownerInfo: state.ownerInfo || null,
        communityInfo: state.communityInfo || null,
        houseInfo: state.houseInfo || null
      })
    } catch (error) {
      console.error('[RefreshState] 刷新页面状态失败:', error)
    }
  }

  // 混入水印功能到所有页面
  options = mixinWatermark(options)

  return originalPage(options)
}

App({
  umengConfig: {
    appKey: '683725ec79267e0210735d8f',
    useOpenid: true,
    autoGetOpenid: true,
    debug: false, // 生产环境关闭调试
    uploadUserInfo: true
  },
  globalData: {
    baseUrl: 'https://xtx.vip.cpolar.cn', // 实际开发中替换为真实的接口地址
    communityInfo: null, // 社区信息
    isLogin: false,
    userInfo: null,
    tokenUser: null
  },

  onLaunch(options) {
    // 初始化状态管理器，它会自动处理登录状态检查
    getStateManager()

    // 初始化token管理器
    this.initTokenManager()

    // 初始化截屏控制
    this.initScreenshotControl()

    // 初始化版本更新管理器
    this.initUpdateManager()

    // 检查登录状态
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('wxUserInfo')

    if (token && userInfo) {
      // 验证token是否有效
      this.checkToken()
    }
  },

  onShow(options) {
    // 小程序显示时的处理
    console.log('[App] 小程序显示', options)
    this.handleAppShow()
  },

  onHide() {
    // 小程序隐藏时记录时间
    console.log('[App] 小程序隐藏')
    this.globalData.hideTime = Date.now()
  },

  onUnload() {
    // 小程序卸载时清理资源
    console.log('[App] 小程序卸载，清理资源')
    try {
      // 清理TokenManager
      const tokenManager = require('./utils/tokenManager.js').default
      if (tokenManager && typeof tokenManager.cleanup === 'function') {
        tokenManager.cleanup()
      }

      // 清理RequestManager
      const requestManager = require('./utils/requestManager.js').default
      if (requestManager && typeof requestManager.destroy === 'function') {
        requestManager.destroy()
      }
    } catch (error) {
      console.error('[App] 清理资源失败:', error)
    }
  },

  // 初始化token管理器
  initTokenManager() {
    try {
      const tokenManager = require('./utils/tokenManager.js').default
      if (tokenManager && typeof tokenManager.startTokenCheck === 'function') {
        tokenManager.startTokenCheck()
        console.log('[App] Token管理器初始化成功')
      }
    } catch (error) {
      console.error('[App] Token管理器初始化失败:', error)
    }
  },

  // 初始化版本更新管理器
  initUpdateManager() {
    try {
      const updateManager = getUpdateManager()
      const initialized = updateManager.init()

      if (initialized) {
        console.log('[App] 版本更新管理器初始化成功')

        // 延迟一下再检查更新，避免与其他初始化冲突
        setTimeout(() => {
          updateManager.autoCheckForUpdate()
        }, 2000)
      } else {
        console.log('[App] 版本更新管理器初始化失败或不支持')
      }
    } catch (error) {
      console.error('[App] 版本更新管理器初始化失败:', error)
    }
  },

  // 处理小程序显示
  async handleAppShow() {
    const token = wx.getStorageSync('token')
    if (!token) {
      return // 没有token，无需处理
    }

    // 检查配置是否需要更新
    await this.checkConfigUpdate()

    // 检查后台运行时间
    const hideTime = this.globalData.hideTime
    if (hideTime) {
      const backgroundTime = Date.now() - hideTime
      const maxBackgroundTime = 30 * 60 * 1000 // 30分钟

      if (backgroundTime > maxBackgroundTime) {
        console.log(`[App] 后台运行${Math.round(backgroundTime / 1000 / 60)}分钟，验证token状态`)
        await this.checkToken()
      }
    } else {
      // 如果没有hideTime，可能是系统重启或首次启动，进行一次token验证
      console.log('[App] 检测到可能的系统重启或首次启动，验证token状态')
      await this.checkToken()
    }

    // 重置隐藏时间
    this.globalData.hideTime = null
  },

  // 检查配置更新
  async checkConfigUpdate() {
    try {
      const { getStateManager } = require('./utils/stateManager.js')
      const { getStatusDataManager, FETCH_MODES } = require('./utils/statusDataManager.js')
      const stateManager = getStateManager()
      const statusManager = getStatusDataManager()

      // 使用StatusDataManager获取最新的社区信息
      // 优先使用缓存，避免与页面的请求冲突
      const result = await statusManager.getStatusData({
        mode: FETCH_MODES.CACHE_FIRST,
        params: {},
        silent: true
      })

      if (result.success && result.data && result.data.communityInfo) {
        // 检查配置是否需要更新
        if (stateManager.checkConfigUpdate(result.data.communityInfo)) {
          console.log('[App] 检测到配置更新，刷新配置')

          // StatusDataManager已经更新了状态管理器，这里不需要重复更新

          // 通知当前页面配置已更新（如果是首页）
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          if (currentPage && currentPage.route === 'pages/index/index' && typeof currentPage.onConfigUpdate === 'function') {
            currentPage.onConfigUpdate()
          }
        }
      }
    } catch (error) {
      console.warn('[App] 配置更新检查失败:', error)
      // 配置检查失败不影响正常流程
    }
  },

  // 验证token是否有效
  async checkToken() {
    try {
      // 获取存储的token和tokenUser
      const token = wx.getStorageSync('token')
      const tokenUser = wx.getStorageSync('tokenUser')

      if (!token || !tokenUser) {
        console.log('[Auth] Token或用户信息缺失，但不清除状态（永不过期机制）')
        return
      }

      const checkRes = await this.request({
        url: '/api/wx/auth/check',
        method: 'POST',
        data: { tokenUser, token }
      })

      if(checkRes.code === 0){
        // 使用状态管理器更新登录状态
        const stateManager = getStateManager()
        stateManager.setState({ isLogin: true })

        // 更新token（如果服务器返回了新的token）
        if(checkRes.token){
          wx.setStorageSync('token', checkRes.token);
        }

        // 更新tokenUser（如果服务器返回了新的tokenUser）
        if(checkRes.tokenUser){
          stateManager.setState({ tokenUser: checkRes.tokenUser })
          wx.setStorageSync('tokenUser', checkRes.tokenUser);
        }
      } else {
        console.log('[Auth] Token验证失败，但保持登录状态（永不过期机制）')
        // 启动token管理器进行后续处理
        try {
          const tokenManager = require('./utils/tokenManager.js').default
          if (tokenManager && typeof tokenManager.startTokenCheck === 'function') {
            tokenManager.startTokenCheck()
          }
        } catch (e) {
          console.error('[Auth] 启动token检查失败:', e)
        }
      }
    } catch (error) {
      console.error('[Auth] Token验证失败:', error)

      // 网络错误时不清除登录状态，给用户重试机会
      if (this.isNetworkError(error)) {
        console.log('[Auth] 网络错误，保持登录状态，等待网络恢复')

        // 启动token管理器的定时检查，网络恢复后会自动验证
        try {
          const tokenManager = require('./utils/tokenManager.js').default
          if (tokenManager && typeof tokenManager.startTokenCheck === 'function') {
            tokenManager.startTokenCheck()
          }
        } catch (e) {
          console.error('[Auth] 启动token检查失败:', e)
        }
        return;
      }

      // token验证异常，但保持登录状态（永不过期机制）
      console.log('[Auth] Token验证异常，但保持登录状态，等待后续恢复')

      // 记录网络错误时间，供状态管理器参考
      if (this.isNetworkError(error)) {
        this.globalData.lastNetworkError = Date.now()
      }
    }
  },

  // 清除登录状态
  clearLoginState(showMessage = true) {
    // 防抖处理，避免多次跳转登录页
    if (this._clearingLoginState) return;
    this._clearingLoginState = true;

    // 延长防抖时间，确保不会重复执行
    setTimeout(() => { this._clearingLoginState = false; }, 5000);

    // 使用状态管理器清除状态
    const stateManager = getStateManager()
    stateManager.clearState()

    // 检查当前页面，避免重复跳转
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : '';

    // 如果已经在登录页，不需要跳转
    if (currentRoute === 'pages/login/index') {
      return;
    }

    // 显示用户友好的提示信息
    if (showMessage) {
      wx.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none',
        duration: 2000,
        success: () => {
          // 延迟跳转，让用户看到提示
          setTimeout(() => {
            this.navigateToLogin();
          }, 1000);
        }
      });
    } else {
      this.navigateToLogin();
    }
  },

  // 跳转到登录页
  navigateToLogin() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : '';

    // 如果是首页，跳转到demo页面
    if (currentRoute === 'pages/index/index') {
      wx.redirectTo({
        url: '/pages/demo/index'
      });
      return;
    }

    // 如果页面栈过深，使用 reLaunch 清空页面栈
    if (pages.length > 3) {
      wx.reLaunch({
        url: '/pages/login/index'
      });
    } else {
      // 否则使用 redirectTo 替换当前页面
      wx.redirectTo({
        url: '/pages/login/index'
      });
    }
  },

  // 网络请求封装（支持token自动刷新）
  request(options) {
    const baseUrl = this.globalData.baseUrl
    return new Promise((resolve, reject) => {
      // 动态获取最新的token
      const token = wx.getStorageSync('token')

      // 设置请求头
      const headers = {
        'Content-Type': 'application/json',
        ...options.header
      }

      // 如果有token，添加到请求头，并加上Bearer前缀
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      wx.request({
        ...options,
        url: `${baseUrl}${options.url}`,
        header: headers,
        success: (res) => {
          // 检查响应头中是否有新token并自动更新
          this.checkAndUpdateTokenFromResponse(res)

          if (res.statusCode === 401) {
            this.clearLoginState(true);
            reject(new Error('登录已过期，请重新登录'));
            return;
          }
          if (res.statusCode === 200) {
            if (res.data.code === 401) {
              // token 失效，清除登录状态
              this.clearLoginState(true)
              reject(new Error('登录已过期，请重新登录'))
            } else {
              resolve(res.data)
            }
          } else {
            const errorMessage = this.getErrorMessage(res.statusCode, res.data)
            reject(new Error(errorMessage))
          }
        },
        fail(err) {
          console.error('[Network] 请求失败:', err)
          reject(new Error(err.errMsg || '网络错误'))
        }
      })
    })
  },

  // 带缓存的请求
  requestWithCache(options, cacheTime = 0) {
    const requestManager = this.getRequestManager()
    return requestManager.requestWithCache(options, cacheTime)
  },

  // 批量请求
  batchRequest(batchKey, options, delay = 50) {
    const requestManager = this.getRequestManager()
    return requestManager.batchRequest(batchKey, options, delay)
  },

  // 获取请求管理器
  getRequestManager() {
    if (!this._requestManager) {
      // 动态导入，避免循环依赖
      const requestManager = require('./utils/requestManager.js').default
      this._requestManager = requestManager
    }
    return this._requestManager
  },

  // 检查响应头中的新token并自动更新
  checkAndUpdateTokenFromResponse(response) {
    if (response && response.header) {
      const newToken = response.header['New-Token'] || response.header['new-token']
      const tokenRefreshed = response.header['Token-Refreshed'] || response.header['token-refreshed']

      if (newToken && tokenRefreshed === 'true') {
        console.log('[App] 检测到新token，自动更新')
        const cleanToken = newToken.replace('Bearer ', '')
        wx.setStorageSync('token', cleanToken)

        // 更新状态管理器中的token相关状态
        const stateManager = this.getStateManager && this.getStateManager()
        if (stateManager) {
          // 触发状态同步，确保全局状态一致
          stateManager.loadFromStorage()
        }

        return true
      }
    }
    return false
  },

  // 检测是否为网络错误
  isNetworkError(error) {
    if (!error) return false;

    // 检查错误消息
    const errorMessage = error.message || error.errMsg || '';
    const networkKeywords = ['网络', 'network', 'timeout', 'fail', 'request:fail'];

    // 检查是否包含网络相关关键词
    const hasNetworkKeyword = networkKeywords.some(keyword =>
      errorMessage.toLowerCase().includes(keyword.toLowerCase())
    );

    // 检查错误码
    const networkErrorCodes = ['NETWORK_ERROR', 'TIMEOUT', 'CONNECTION_FAILED'];
    const hasNetworkCode = networkErrorCodes.includes(error.code);

    return hasNetworkKeyword || hasNetworkCode;
  },

  // 获取友好的错误信息
  getErrorMessage(statusCode, data) {
    const errorMap = {
      400: '请求参数错误',
      401: '登录已过期，请重新登录',
      403: '权限不足',
      404: '请求的资源不存在',
      500: '服务器繁忙，请稍后重试',
      502: '网关错误',
      503: '服务暂不可用'
    }

    return errorMap[statusCode] || data?.msg || '请求失败'
  },

  // 初始化截屏控制
  initScreenshotControl() {
    try {
      // 延迟执行，确保配置已经加载
      setTimeout(() => {
        const { getStateManager } = require('./utils/stateManager.js')
        const stateManager = getStateManager()

        // 通过状态管理器更新截屏控制
        stateManager.updateScreenshotControl()
      }, 1000)
    } catch (error) {
      console.error('[App] 初始化截屏控制失败:', error)
    }
  }
})
