<!--index.wxml-->

<!-- 水印组件 -->
<watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></watermark>

<!-- 页面loading状态 -->
<view class="page-loading" wx:if="{{pageLoading}}">
  <van-loading type="spinner" size="48rpx" color="#1890ff">加载中...</van-loading>
</view>

<!-- 添加到桌面引导弹窗 -->
<view class="desktop-guide-overlay" wx:if="{{showDesktopGuide}}" bindtap="hideDesktopGuide">
  <view class="desktop-guide-modal" catchtap="preventClose">
    <view class="guide-header">
      <text class="guide-title">添加到桌面</text>
      <van-icon name="cross" size="32rpx" color="#666" bindtap="hideDesktopGuide" />
    </view>
    <view class="guide-content">
      <view class="guide-step">
        <text class="step-number">1</text>
        <text class="step-text">点击右上角"..."菜单</text>
      </view>
      <view class="guide-step">
        <text class="step-number">2</text>
        <text class="step-text">选择"添加到桌面"</text>
      </view>
      <view class="guide-step">
        <text class="step-number">3</text>
        <text class="step-text">下次可直接从桌面打开</text>
      </view>
    </view>
    <view class="guide-actions">
      <button class="guide-btn secondary" bindtap="hideDesktopGuide">我知道了</button>
      <button class="guide-btn primary" bindtap="confirmDesktopGuide">不再提示</button>
    </view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container">
    <!-- 顶部整体区域 -->
    <view class="header-wrapper">
      <!-- 背景图片 -->
      <image class="header-bg" src="/static/images/index-bg.png" mode="aspectFill"></image>

      <!-- 自定义导航栏 -->
      <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
        <view class="navbar-content" bindtap="goToHouseList">
          <view class="location-wrapper">
            <text class="navbar-location">{{communityInfo.displayName || communityInfo.communityName || '睦邻共治'}}</text>
            <van-icon name="exchange" size="32rpx" color="#fff" custom-style="margin-left: 8rpx;" />
          </view>
        </view>
      </view>

      <!-- 顶部横幅区域 -->
      <view class="banner-section" wx:if="{{enableMiniprogramTitle}}">
        <view class="banner-title" wx:if="{{miniprogramTitle}}">
          {{miniprogramTitle}}
        </view>
        <view class="banner-title" wx:else>
          智享社区，贴心到家，共同成长，共创未来！
        </view>

        <view class="banner-slogan" wx:if="{{miniprogramSubtitle}}">
          {{miniprogramSubtitle}}
        </view>
        <view class="banner-slogan" wx:else>
          物业服务就在掌心，共同成长，共创未来！
        </view>
      </view>
    </view>

    <!-- 主功能卡片区域 -->
    <view class="main-cards" wx:if="{{showBigCard}}">
      <view class="card-item {{card.cardStyle}}"
            wx:for="{{dynamicMainCards}}"
            wx:key="nav_id"
            wx:for-item="card"
            bindtap="handleMainCardTap"
            data-card="{{card}}">
        <view class="card-content">
          <view class="card-text">
            <text class="card-title">{{card.nav_name}}</text>
            <text class="card-desc">{{card.remark}}</text>
          </view>
          <view class="card-icon">
            <van-icon name="{{card.icon_name}}" size="48rpx" color="#fff" />
          </view>
        </view>
      </view>
    </view>



    <!-- 功能网格容器 -->
    <view class="function-container {{!showBigCard ? 'no-main-cards' : ''}}" wx:if="{{functionContainerVisible}}">
      <!-- 物业通知栏 -->
      <view class="notice-bar" wx:if="{{latestNotice}}" bindtap="{{isLogin ? 'goToNewsDetail' : 'goToLogin'}}" data-id="{{latestNotice.id}}">
        <view class="notice-info">
          <van-icon name="volume-o" size="28rpx" color="#1890ff" />
          <text class="notice-text">{{latestNotice.title}}</text>
          <text class="notice-date">{{latestNotice.time}}</text>
        </view>
      </view>

      <!-- 功能网格 -->
      <view class="function-grid">
        <view class="grid-row" wx:for="{{menuRows}}" wx:key="index">
          <view class="grid-item"
                wx:for="{{item}}"
                wx:for-item="menu"
                wx:key="id"
                bindtap="handleMenuTap"
                data-menu="{{menu}}">
            <view class="grid-icon" style="background: {{menu.iconBg}};">
              <van-icon name="{{menu.icon_name || 'apps-o'}}" size="48rpx" color="#fff" />
            </view>
            <text class="grid-text">{{menu.nav_name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 社区活动区域 -->
    <view class="community-section">
      <view class="section-header">
        <view class="section-tabs">
          <view class="tab-item {{currentNoticeType === item.dictValue ? 'active' : ''}}"
                wx:for="{{noticeTypes}}"
                wx:key="dictValue"
                bindtap="switchNoticeType"
                data-type="{{item.dictValue}}">
            {{item.dictLabel}}
          </view>
        </view>
      </view>

      <view class="activity-content">
        <view class="activity-item"
              wx:for="{{categoryNewsList}}"
              wx:key="id"
              bindtap="{{isLogin ? 'goToNewsDetail' : 'goToLogin'}}"
              data-id="{{item.id}}">
          <view class="activity-text">
            <text class="activity-title">{{item.title}}</text>
            <text class="activity-time">{{item.time}}</text>
          </view>
          <view class="activity-image" wx:if="{{item.image}}">
            <image src="{{item.image}}" mode="aspectFill" />
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{categoryNewsList.length === 0}}">
          <van-icon name="smile-o" size="80rpx" color="#ddd" />
          <text class="empty-text">暂时没有新消息哦~</text>
        </view>

        <!-- 查看更多按钮 -->
        <view class="load-more-btn" wx:if="{{hasMoreNews && categoryNewsList.length > 0}}" bindtap="goToNoticeList">
          <text class="load-more-text">查看更多</text>
          <van-icon name="arrow" size="24rpx" color="#1890ff" />
        </view>
      </view>
    </view>
</view>
