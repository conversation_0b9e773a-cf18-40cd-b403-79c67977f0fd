package com.ehome.oc.service.impl;

import com.ehome.common.exception.ServiceException;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.mapper.HouseInfoMapper;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Validator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class HouseInfoServiceImpl implements IHouseInfoService {

    private static final Logger log = LoggerFactory.getLogger(HouseInfoServiceImpl.class);

    @Autowired
    private HouseInfoMapper houseInfoMapper;

    @Autowired
    private Validator validator;


    @Override
    public HouseInfo selectHouseById(Long houseId) {
        return houseInfoMapper.selectHouseById(houseId);
    }


    @Override
    public HouseInfo recordToObj(Record record){
        HouseInfo house = new HouseInfo();
        house.setHouseId(record.getLong("house_id"));
        house.setIsDefault(record.getInt("user_default"));
        house.setHouseStatus(record.getStr("house_status"));
        house.setCheckStatus(record.getInt("check_status"));
        house.setStatus(record.getInt("check_status"));
        house.setCommunityName(record.getStr("community_name"));
        house.setCommunityId(record.getStr("community_id"));
        house.setBuildingId(record.getStr("building_id"));
        house.setBuilding(record.getStr("building_name")); // 从查询结果中获取楼栋名称
        house.setUnit(record.getStr("unit_name")); // 从查询结果中获取单元名称
        house.setRoom(record.getStr("room"));
        house.setCombinaName(record.getStr("combina_name")); // 添加房屋全称
        house.setArea(record.getBigDecimal("area"));
        house.setOwnerName(record.getStr("owner_name"));
        house.setOwnerId(record.getStr("owner_id"));
        house.setOwnerPhone(record.getStr("owner_phone"));

        house.setIdCard(record.getStr("id_card"));
        house.setRemark(record.getStr("remark"));

        // 设置住户信息字符串
        house.setOwnerStr(record.getStr("owner_str"));

        // 添加关系类型信息，用于前端显示住户类型
        // 使用houseStatus字段临时存储关系类型（因为这个字段在房屋列表中不常用）
        Integer relType = record.getInt("rel_type");
        if (relType != null) {
            house.setHouseStatus(relType.toString());
        }
        house.setHouseId(record.getLong("house_id"));
        house.setCombinaName(record.getStr("combina_name"));
        return house;
    }

    @Override
    @Transactional
    public int addHouse(HouseInfo house) {
        house.setCheckStatus(0);
        return houseInfoMapper.insertHouse(house);
    }

    @Override
    public int deleteHouse(Long houseId) {
        // 检查是否为默认房屋
        HouseInfo house = houseInfoMapper.selectHouseById(houseId);
        if (house != null && house.getIsDefault() == 1) {
            throw new ServiceException("默认房屋不能删除");
        }
        return houseInfoMapper.deleteHouseById(houseId);
    }

    @Override
    @Transactional
    public int setDefaultHouse(Long houseId, String ownerId) {
        int result = 0;
        // 获取当前业主的手机号和小区信息
        try{
            Record ownerRecord = Db.findFirst("SELECT mobile, community_id FROM eh_owner WHERE owner_id = ?", ownerId);
            if (ownerRecord == null) {
                throw new ServiceException("业主信息不存在");
            }

            String mobile = ownerRecord.getStr("mobile");
            String communityId = ownerRecord.getStr("community_id");

            // 1. 先重置该手机号下所有业主的默认状态
            Db.update("UPDATE eh_owner SET is_default = 0 WHERE mobile = ?", mobile);

            // 2. 重置该业主的所有默认房屋
            Db.update("UPDATE eh_house_owner_rel SET is_default = 0 WHERE owner_id = ?", ownerId);

            // 3. 设置当前业主为默认业主
            Db.update("UPDATE eh_owner SET is_default = 1 WHERE owner_id = ?", ownerId);

            // 4. 更新业主的默认房屋信息
            String combinaName = Db.queryStr("select concat(combina_name,'-',room) from eh_house_info where house_id = ?", houseId);
            Db.update("update eh_owner set house_id = ?, house_name = ? where owner_id = ?", houseId, combinaName, ownerId);

            // 5. 设置新的默认房屋
            Db.update("UPDATE eh_house_owner_rel SET is_default = 1 WHERE house_id = ? and owner_id = ?", houseId, ownerId);

            // 6. 更新微信用户表中的业主ID和小区ID
            Db.update("UPDATE eh_wx_user SET owner_id = ?, community_id = ? WHERE mobile = ?", ownerId, communityId, mobile);
            result = 1;
        }catch(Exception e){
            log.error("设置默认房屋失败", e);
            throw new ServiceException("设置默认房屋失败: " + e.getMessage());
        }
        return result;
    }

    @Override
    public void updateOwnerHouseInfo(String ownerId) {
        // 更新业主的房屋数量 - 使用关联表计算实际数量
        Db.update("UPDATE eh_owner o SET house_count = (SELECT COUNT(*) FROM eh_house_owner_rel r WHERE r.owner_id = o.owner_id and r.check_status = 1) WHERE o.owner_id = ?", ownerId);
        // 更新业主的房屋信息字符串
        Db.update("update eh_owner t1 set t1.house_info = (SELECT GROUP_CONCAT(CONCAT(t2.combina_name,'-',t2.room)) from eh_house_info t2 ,eh_house_owner_rel t3 where t3.house_id = t2.house_id and t1.owner_id = t3.owner_id and t3.check_status = 1) where t1.owner_id = ?", ownerId);
    }

    @Override
    public void updateHouseOwnerInfo(String houseId) {
        // 更新房屋的住户数量 - 使用关联表计算实际数量
        Db.update("UPDATE eh_house_info h SET owner_count = (SELECT COUNT(*) FROM eh_house_owner_rel r WHERE r.house_id = h.house_id and r.check_status = 1) WHERE h.house_id = ?", houseId);
        // 更新房屋的业主字符串
        Db.update("UPDATE eh_house_info t1 JOIN ( SELECT t3.house_id, GROUP_CONCAT(t2.owner_name) AS owner_str FROM eh_owner t2 JOIN eh_house_owner_rel t3 ON t2.owner_id = t3.owner_id  and t3.check_status = 1 GROUP BY t3.house_id) AS t4 ON t1.house_id = t4.house_id SET t1.owner_str = t4.owner_str WHERE t1.house_id = ?", houseId);
    }

    @Override
    public int countHouseByUserId(String ownerId) {
        return Db.queryInt("SELECT COUNT(*) FROM eh_house_owner_rel WHERE owner_id = ? and check_status = 1", ownerId);
    }

    @Override
    public String importHouse(List<HouseInfo> houseList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(houseList) || houseList.size() == 0) {
            throw new ServiceException("导入房屋数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 缓存楼栋和单元信息，避免重复查询
        Map<String, String> buildingCache = new HashMap<>(); // key: 楼栋名称, value: 楼栋ID
        Map<String, String> unitCache = new HashMap<>(); // key: 楼栋ID_单元名称, value: 单元ID

        for (HouseInfo house : houseList) {
            try {
                // 验证必填字段
                if (StringUtils.isEmpty(house.getBuilding()) || StringUtils.isEmpty(house.getUnit()) ||
                    StringUtils.isEmpty(house.getRoom()) || StringUtils.isEmpty(house.getFloor()) ||
                    house.getUseArea() == null || house.getTotalArea() == null) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、房间 " + house.getRoom() + " 导入失败：必填字段不能为空");
                    continue;
                }

                // 根据楼栋名称查找楼栋ID（使用缓存）
                String buildingCacheKey = house.getBuilding() + "_" + house.getCommunityId();
                String buildingId = buildingCache.get(buildingCacheKey);
                if (buildingId == null) {
                    Record buildingRecord = Db.findFirst("SELECT building_id FROM eh_building WHERE name = ? AND community_id = ?", house.getBuilding(), house.getCommunityId());
                    if (buildingRecord == null) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、房间 " + house.getRoom() + " 导入失败：楼栋 " + house.getBuilding() + " 不存在");
                        continue;
                    }
                    buildingId = buildingRecord.getStr("building_id");
                    buildingCache.put(buildingCacheKey, buildingId); // 缓存楼栋ID
                }
                house.setBuildingId(buildingId);

                // 根据单元名称查找单元ID（使用缓存）
                String unitCacheKey = buildingId + "_" + house.getUnit();
                String unitId = unitCache.get(unitCacheKey);
                if (unitId == null) {
                    Record unitRecord = Db.findFirst("SELECT unit_id FROM eh_unit WHERE name = ? AND building_id = ?", house.getUnit(), buildingId);
                    if (unitRecord == null) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、房间 " + house.getRoom() + " 导入失败：单元 " + house.getUnit() + " 不存在");
                        continue;
                    }
                    unitId = unitRecord.getStr("unit_id");
                    unitCache.put(unitCacheKey, unitId); // 缓存单元ID
                }
                house.setUnitId(unitId);

                // 检查房间号是否重复（同一楼栋同一单元同一楼层内）
                Record existingHouse = Db.findFirst("SELECT * FROM eh_house_info WHERE room = ? AND building_id = ? AND unit_id = ? AND floor = ? AND community_id = ?", house.getRoom(), buildingId, unitId, house.getFloor(), house.getCommunityId());

                if (existingHouse != null) {
                    if (isUpdateSupport) {
                        // 更新现有房屋
                        Record updateRecord = new Record();
                        updateRecord.set("house_id", existingHouse.getLong("house_id"));
                        updateRecord.set("floor", house.getFloor());
                        updateRecord.set("use_area", house.getUseArea());
                        updateRecord.set("total_area", house.getTotalArea());
                        updateRecord.set("house_type", house.getHouseType());
                        updateRecord.set("house_status", house.getHouseStatus());
                        updateRecord.set("remark", house.getRemark());
                        updateRecord.set("update_by", operName);
                        updateRecord.set("update_time", DateUtils.getTime());

                        Db.update("eh_house_info", "house_id", updateRecord);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、房间 " + house.getRoom() + " 更新成功");
                    } else {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、房间 " + house.getRoom() + " 导入失败：房间号已存在");
                    }
                    continue;
                }

                // 新增房屋
                Record houseRecord = new Record();
                houseRecord.set("community_id", house.getCommunityId());
                houseRecord.set("building_id", buildingId);
                houseRecord.set("building_name", house.getBuilding());
                houseRecord.set("unit_id", unitId);
                houseRecord.set("unit_name", house.getUnit());
                houseRecord.set("room", house.getRoom());
                houseRecord.set("floor", house.getFloor());
                houseRecord.set("use_area", house.getUseArea());
                houseRecord.set("total_area", house.getTotalArea());
                houseRecord.set("house_type", house.getHouseType());
                houseRecord.set("house_status", house.getHouseStatus());
                houseRecord.set("check_status", 1); // 默认审核通过
                houseRecord.set("remark", house.getRemark());
                houseRecord.set("create_by", operName);
                houseRecord.set("create_time", DateUtils.getTime());

                // 生成房屋全称
                String combinaName = house.getBuilding() +"-" +house.getUnit()+"-"+house.getFloor();
                houseRecord.set("combina_name", combinaName);

                Db.save("eh_house_info", "house_id", houseRecord);
                successNum++;
                successMsg.append("<br/>" + successNum + "、房间 " + house.getRoom() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String room = house.getRoom();
                String msg = "<br/>" + failureNum + "、房间 " + room + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            // 更新楼栋和单元的房屋数量统计
            updateBuildingAndUnitHouseCount();
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 更新楼栋和单元的房屋数量统计
     */
    private void updateBuildingAndUnitHouseCount() {
        // 更新楼栋的房屋数量
        Db.update("UPDATE eh_building b SET house_count = (SELECT COUNT(*) FROM eh_house_info h WHERE h.building_id = b.building_id)");

        // 更新单元的房屋数量
        Db.update("UPDATE eh_unit u SET house_count = (SELECT COUNT(*) FROM eh_house_info h WHERE h.unit_id = u.unit_id)");
    }

    @Override
    public void refreshCombineName(String communityId) {
        log.info("开始刷新房屋全称字段，小区ID: {}", communityId);
        long startTime = System.currentTimeMillis();

        try {
            String sql = "UPDATE eh_house_info h " +
                        "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                        "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                        "SET h.combina_name = CASE " +
                        "  WHEN u.name IS NOT NULL AND u.name != '' THEN CONCAT(IFNULL(b.name, ''), '-', u.name) " +
                        "  ELSE IFNULL(b.name, '') " +
                        "END";

            if (StringUtils.isNotEmpty(communityId)) {
                sql += " WHERE h.community_id = ?";
                Db.update(sql, communityId);
            } else {
                Db.update(sql);
            }

            long endTime = System.currentTimeMillis();
            log.info("房屋全称字段刷新完成，耗时: {}ms", endTime - startTime);
        } catch (Exception e) {
            log.error("刷新房屋全称字段失败", e);
            throw new ServiceException("刷新房屋全称字段失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void refreshAllStatistics(String communityId) {
        log.info("开始刷新所有统计字段，小区ID: {}", communityId);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 先刷新房屋全称
            refreshCombineName(communityId);

            // 2. 刷新业主表统计字段
            refreshOwnerStatistics(communityId);

            // 3. 刷新房屋表统计字段
            refreshHouseStatistics(communityId);

            // 4. 刷新车位表统计字段
            refreshParkingStatistics(communityId);

            // 5. 刷新车辆表统计字段
            refreshVehicleStatistics(communityId);

            long endTime = System.currentTimeMillis();
            log.info("所有统计字段刷新完成，耗时: {}ms", endTime - startTime);
        } catch (Exception e) {
            log.error("刷新统计字段失败", e);
            throw new ServiceException("刷新统计字段失败: " + e.getMessage());
        }
    }

    /**
     * 刷新业主表统计字段
     */
    private void refreshOwnerStatistics(String communityId) {
        log.info("开始刷新业主表统计字段");

        try {
            String whereClause = StringUtils.isNotEmpty(communityId) ? " WHERE o.community_id = ?" : "";

            // 刷新业主房屋数量
            String houseSql = "UPDATE eh_owner o SET house_count = (" +
                            "SELECT COUNT(*) FROM eh_house_owner_rel r " +
                            "WHERE r.owner_id = o.owner_id AND r.check_status = 1)" + whereClause;

            // 刷新业主房屋信息字符串
            String houseInfoSql = "UPDATE eh_owner o SET house_info = (" +
                                "SELECT GROUP_CONCAT(CONCAT(h.combina_name,'-',h.room)) " +
                                "FROM eh_house_info h " +
                                "JOIN eh_house_owner_rel r ON h.house_id = r.house_id " +
                                "WHERE r.owner_id = o.owner_id AND r.check_status = 1)" + whereClause;

            // 刷新业主车位数量
            String parkingSql = "UPDATE eh_owner o SET parking_count = (" +
                              "SELECT COUNT(*) FROM eh_parking_owner_rel r " +
                              "WHERE r.owner_id = o.owner_id AND r.check_status = 1)" + whereClause;

            // 刷新业主车位号字符串
            String parkingNoSql = "UPDATE eh_owner o SET parking_no = (" +
                                "SELECT GROUP_CONCAT(p.parking_no) " +
                                "FROM eh_parking_space p " +
                                "JOIN eh_parking_owner_rel r ON p.parking_id = r.parking_id " +
                                "WHERE r.owner_id = o.owner_id AND r.check_status = 1)" + whereClause;

            // 刷新业主车辆数量
            String carCountSql = "UPDATE eh_owner o SET car_count = (" +
                               "SELECT COUNT(*) FROM eh_vehicle_owner_rel r " +
                               "WHERE r.owner_id = o.owner_id AND r.check_status = 1)" + whereClause;

            // 刷新业主车辆信息字符串
            String carInfoSql = "UPDATE eh_owner o SET car_info = (" +
                              "SELECT GROUP_CONCAT(v.plate_no) " +
                              "FROM eh_vehicle v " +
                              "JOIN eh_vehicle_owner_rel r ON v.vehicle_id = r.vehicle_id " +
                              "WHERE r.owner_id = o.owner_id AND r.check_status = 1)" + whereClause;

            if (StringUtils.isNotEmpty(communityId)) {
                Db.update(houseSql, communityId);
                Db.update(houseInfoSql, communityId);
                Db.update(parkingSql, communityId);
                Db.update(parkingNoSql, communityId);
                Db.update(carCountSql, communityId);
                Db.update(carInfoSql, communityId);
            } else {
                Db.update(houseSql);
                Db.update(houseInfoSql);
                Db.update(parkingSql);
                Db.update(parkingNoSql);
                Db.update(carCountSql);
                Db.update(carInfoSql);
            }

            log.info("业主表统计字段刷新完成");
        } catch (Exception e) {
            log.error("刷新业主表统计字段失败", e);
            throw e;
        }
    }

    /**
     * 刷新房屋表统计字段
     */
    private void refreshHouseStatistics(String communityId) {
        log.info("开始刷新房屋表统计字段");

        try {
            String whereClause = StringUtils.isNotEmpty(communityId) ? " WHERE h.community_id = ?" : "";

            // 刷新房屋业主数量
            String ownerCountSql = "UPDATE eh_house_info h SET owner_count = (" +
                                 "SELECT COUNT(*) FROM eh_house_owner_rel r " +
                                 "WHERE r.house_id = h.house_id AND r.check_status = 1)" + whereClause;

            // 刷新房屋业主字符串
            String ownerStrSql = "UPDATE eh_house_info h SET owner_str = (" +
                               "SELECT GROUP_CONCAT(o.owner_name) " +
                               "FROM eh_owner o " +
                               "JOIN eh_house_owner_rel r ON o.owner_id = r.owner_id " +
                               "WHERE r.house_id = h.house_id AND r.check_status = 1)" + whereClause;

            if (StringUtils.isNotEmpty(communityId)) {
                Db.update(ownerCountSql, communityId);
                Db.update(ownerStrSql, communityId);
            } else {
                Db.update(ownerCountSql);
                Db.update(ownerStrSql);
            }

            log.info("房屋表统计字段刷新完成");
        } catch (Exception e) {
            log.error("刷新房屋表统计字段失败", e);
            throw e;
        }
    }

    /**
     * 刷新车位表统计字段
     */
    private void refreshParkingStatistics(String communityId) {
        log.info("开始刷新车位表统计字段");

        try {
            String whereClause = StringUtils.isNotEmpty(communityId) ? " WHERE p.community_id = ?" : "";

            // 刷新车位业主数量
            String ownerCountSql = "UPDATE eh_parking_space p SET owner_count = (" +
                                 "SELECT COUNT(*) FROM eh_parking_owner_rel r " +
                                 "WHERE r.parking_id = p.parking_id AND r.check_status = 1)" + whereClause;

            // 刷新车位业主名称字符串
            String ownerNameSql = "UPDATE eh_parking_space p SET owner_name = (" +
                                "SELECT GROUP_CONCAT(o.owner_name) " +
                                "FROM eh_owner o " +
                                "JOIN eh_parking_owner_rel r ON o.owner_id = r.owner_id " +
                                "WHERE r.parking_id = p.parking_id AND r.check_status = 1)" + whereClause;

            if (StringUtils.isNotEmpty(communityId)) {
                Db.update(ownerCountSql, communityId);
                Db.update(ownerNameSql, communityId);
            } else {
                Db.update(ownerCountSql);
                Db.update(ownerNameSql);
            }

            log.info("车位表统计字段刷新完成");
        } catch (Exception e) {
            log.error("刷新车位表统计字段失败", e);
            throw e;
        }
    }

    /**
     * 刷新车辆表统计字段
     */
    private void refreshVehicleStatistics(String communityId) {
        log.info("开始刷新车辆表统计字段");

        try {
            String whereClause = StringUtils.isNotEmpty(communityId) ? " WHERE v.community_id = ?" : "";

            // 刷新车辆业主数量
            String ownerCountSql = "UPDATE eh_vehicle v SET owner_count = (" +
                                 "SELECT COUNT(*) FROM eh_vehicle_owner_rel r " +
                                 "WHERE r.vehicle_id = v.vehicle_id AND r.check_status = 1)" + whereClause;

            // 刷新车辆业主名称字符串
            String ownerNameSql = "UPDATE eh_vehicle v SET owner_name = (" +
                                "SELECT GROUP_CONCAT(CONCAT(o.owner_name, '(', o.mobile, ')')) " +
                                "FROM eh_owner o " +
                                "JOIN eh_vehicle_owner_rel r ON o.owner_id = r.owner_id " +
                                "WHERE r.vehicle_id = v.vehicle_id AND r.check_status = 1)" + whereClause;

            if (StringUtils.isNotEmpty(communityId)) {
                Db.update(ownerCountSql, communityId);
                Db.update(ownerNameSql, communityId);
            } else {
                Db.update(ownerCountSql);
                Db.update(ownerNameSql);
            }

            log.info("车辆表统计字段刷新完成");
        } catch (Exception e) {
            log.error("刷新车辆表统计字段失败", e);
            throw e;
        }
    }
}